import { useAuthStore, useSocketStore } from "@/app/store/stores";
import { handleLogout } from "@/helpers/handleLogout";
import useGameConfig from "@/hooks/useGameConfig";
import posthog from "posthog-js";
import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";

const AUTH_PROVIDERS = {
    CREDENTIALS: "credentials",
    DISCORD: "discord",
    GOOGLE: "google",
} as const;

const AuthRedirect = () => {
    const navigate = useNavigate();
    // Force fetch user data on callback route regardless of auth state
    const { data, error, isLoading } = useFetchCurrentUser({ enabled: true });
    const setAuthed = useAuthStore((state) => state.setAuthed);

    const { fetchSocket } = useSocketStore();
    const { LOGIN_DISABLED } = useGameConfig();
    const [searchParams] = useSearchParams();
    const auth = searchParams.get("auth");
    const isRegister = searchParams.get("register") === "true";
    const email = data?.email;

    useEffect(() => {
        if (isLoading) return;

        if (LOGIN_DISABLED || !auth || !Object.values(AUTH_PROVIDERS).includes(auth as any)) {
            handleLogout();
            return;
        }

        if (data && !error) {
            // Check if this is an OAuth user who needs to set a proper username
            if (auth === "discord" || auth === "google") {
                const username = data?.username;
                const userEmail = data?.email;

                // Check if user has a default username pattern (email prefix or similar)
                if (
                    username &&
                    userEmail &&
                    (username === userEmail.split("@")[0] ||
                        username.includes("@") ||
                        username.length < 4 ||
                        /^user\d+$/.test(username)) // Pattern like "user123"
                ) {
                    // Redirect to username selection for OAuth users with default usernames
                    navigate(`/register?auth=${auth}&oauthSetup=true`);
                    return;
                }
            }

            setAuthed(true);
            fetchSocket();

            if (auth && email) {
                posthog.capture(`${auth}_${isRegister ? "register" : "login"}`, { email });
            }
            navigate("/home");
        }

        if (error) {
            console.error(`${auth} Authentication failed: ${error.message}`);
            handleLogout();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isLoading, error, data, navigate, LOGIN_DISABLED, auth]);

    return null;
};

export default AuthRedirect;
