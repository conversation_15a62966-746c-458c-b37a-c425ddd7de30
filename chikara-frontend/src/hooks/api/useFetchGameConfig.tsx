import { api, QueryOptions } from "@/helpers/api";
import { client } from "@/lib/orpc";
import { usePersistStore } from "@/app/store/stores";
import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";

export const fetchGameConfig = async () => {
    return await client.user.getGameConfig();
};

/**
 * Custom hook to fetch game configuration with TanStack Query
 * Uses local storage data initially, only fetches from API when needed
 * Automatically updates the persistent store when data is fetched
 */
export const useFetchGameConfig = (options: QueryOptions = {}) => {
    const { gameConfig, setGameConfig } = usePersistStore();

    const query = useQuery(
        api.user.getGameConfig.queryOptions({
            staleTime: Infinity, // Only refetch when explicitly invalidated
            retry: 3,
            retryDelay: 1000,
            initialData: gameConfig || undefined, // Use local storage data as initial data
            ...options,
        })
    );

    // Update store when data changes
    useEffect(() => {
        if (query.data) {
            setGameConfig(query.data);
        }
    }, [query.data, setGameConfig]);

    return query;
};

export default useFetchGameConfig;
