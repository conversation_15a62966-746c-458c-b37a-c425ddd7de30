import { persistStore } from "@/app/store/persistStore";
import { client } from "@/lib/orpc";

export const fetchGameConfig = async () => {
    const { setGameConfig } = persistStore.getState();

    try {
        const data = await client.user.getGameConfig();

        if (data) {
            setGameConfig(data);
        } else {
            console.error("Failed to fetch game config");
        }

        return { data, error: null };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown error";
        console.error("Failed to fetch game config:", errorMessage);
        return { data: null, error: errorMessage };
    }
};
