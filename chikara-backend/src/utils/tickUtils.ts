import gameConfig from "../config/gameConfig.js";
import { Prisma, PrismaClient } from "@prisma/client";

const tickStartTime = Date.now();
let nextAPRegenTime = Date.now() + gameConfig.AP_TICK_INTERVAL;

export const GetNextHealingTick = function () {
    const currentTime = Date.now();
    const timeSinceLastTick = (currentTime - tickStartTime) % gameConfig.HEALING_TICK_INTERVAL;
    const lastTickTimestamp = currentTime - timeSinceLastTick;
    return lastTickTimestamp + gameConfig.HEALING_TICK_INTERVAL;
};

export const GetNextAPRegenTime = function () {
    return nextAPRegenTime;
};

export const SetNextAPRegenTime = function (time: number) {
    nextAPRegenTime = time;
};
