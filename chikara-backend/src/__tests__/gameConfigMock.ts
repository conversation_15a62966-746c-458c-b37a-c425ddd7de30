import ms from "ms";

// Create a deep clone of an object
const deepClone = (obj: unknown) => structuredClone(obj);

// Start with a minimalist version of the actual config
const originalConfigs = {
    version: 1.49,
    authConfig: {
        public: {
            REGISTRATION_DISABLED: false,
            LOGIN_DISABLED: false,
            D<PERSON>CORD_AUTH_ENABLED: true,
        },
        hidden: {
            RESET_TOKEN_VALID_FOR: ms("10m"),
            SITE_MAINTENANCE_MODE: false,
            REGISTRATION_CHATMESSAGE_DISABLED: false,
        },
    },
    frontendConfig: {
        public: {
            MARQUEE_BANNER_DISABLED: false,
        },
        hidden: {},
    },
    battleConfig: {
        public: {
            PVP_MIN_LVL: 5,
            PVP_BATTLE_AP_COST: 2,
            ROOFTOP_BATTLE_AP_COST: 2,
            MUG_XP: 100,
            CRIPPLE_XP: 100,
            LEAVE_XP: 200,
            NPC_KILL_XP: 100,
            BOSS_KILL_XP: 200,
            DA<PERSON>Y_USER_ATTACK_LIMIT: 3,
            BASE_STAMINA: 100,
        },
        hidden: {
            BATTLE_TIMEOUT_MS: ms("10m"),
            FLEE_CHANCE: 0.4,
            BASE_JAIL_CHANCE: 0.3,
            JAIL_DURATION_MS: ms("10m"),
        },
    },
    bankConfig: {
        public: {
            DEPOSIT_DISABLED: false,
            BANK_DISABLED: false,
            TRANSACTION_FEE: 0.15,
            MINIMUM_DEPOSIT: 300,
            MINIMUM_WITHDRAWAL: 100,
            MINIMUM_TRANSFER: 100,
            TRANSACTION_HISTORY_LIMIT: 10,
        },
        hidden: {},
    },
    chatConfig: {
        public: {
            CHAT_DISABLED: false,
            CHAT_MESSAGE_SENDING_DISABLED: false,
        },
        hidden: {
            MAX_CHAT_HISTORY_LENGTH: 300,
            DISCORD_CHAT_WEBHOOK_ENABLED: true,
        },
    },
    craftingConfig: {
        public: { CRAFTING_ENERGY_COST: 0 },
        hidden: {},
    },
    casinoConfig: {
        public: {
            CASINO_DISABLED: false,
            LOTTERY_DISABLED: false,
            SLOTS_MAX_BET: 5_000_000,
            LOTTERY_TICKET_COST: 2500,
        },
        hidden: {},
    },
    userConfig: {
        public: {
            MAX_LEVEL_CAP: 40,
            HEALING_TICK_INTERVAL: ms("1m"),
            AP_TICK_INTERVAL: ms("10m"),
            ENERGY_TICK_MS: ms("1m"),
            HEALTH_REGEN_AMOUNT: 0.025,
            TRAINING_ENERGY_COST: 20,
            TALENT_RESPEC_BASE_COST: 1000,
            NEW_PLAYER_HOSPITAL_PROTECTION_LEVEL: 15,
        },
        hidden: {
            XP_TO_LEVEL_MULTIPLIER: 800,
        },
    },
    jobsConfig: {
        public: { JOBS_DISABLED: false },
        hidden: {},
    },
    notificationConfig: {
        public: {},
        hidden: {
            MAX_NOTIFICATION_HISTORY_LENGTH: 300,
            AP_NOTIFICATIONS_ENABLED: true,
            ENERGY_NOTIFICATIONS_ENABLED: false,
            HEALTH_NOTIFICATIONS_ENABLED: true,
        },
    },
    levelGatesConfig: {
        public: {
            JOBS_LEVEL_GATE: 4,
            TALENTS_LEVEL_GATE: 5,
            CRAFTING_LEVEL_GATE: 7,
            SHOP1_LEVEL_GATE: 7,
            SHOP2_LEVEL_GATE: 15,
            SHOP3_LEVEL_GATE: 0,
            COURSES_LEVEL_GATE: 8,
            DAILY_QUESTS_LEVEL_GATE: 3,
            ROOFTOP_BATTLES_LEVEL_GATE: 10,
            MARKET_LEVEL_GATE: 12,
        },
        hidden: {},
    },
    leaderboardsConfig: {
        public: { LEADERBOARDS_DISABLED: false, USERS_PER_BOARD: 3 },
        hidden: {},
    },
    roguelikeConfig: {
        public: {
            ROGUELIKE_DISABLED: false,
            ACTION_POINTS_REQUIRED: 1,
            CHURCH_MINIMUM_ZONE_LVL: 3,
            MALL_MINIMUM_ZONE_LVL: 7,
            SHRINE_MINIMUM_ZONE_LVL: 10,
            ALLEY_MINIMUM_ZONE_LVL: 16,
            SEWERS_MINIMUM_ZONE_LVL: 12,
        },
        hidden: {
            DEFAULT_ENCOUNTER_HOSPITAL_DURATION_MS: ms("10m"),
            DEFAULT_ENCOUNTER_JAIL_DURATION_MS: ms("10m"),
            NORMAL_NPC_BATTLE_TIMEOUT_MS: ms("10m"),
            BOSS_BATTLE_TIMEOUT_MS: ms("10m"),
        },
    },
    uniqueItemsConfig: {
        public: {
            ANON_ITEM_NAME: "Balaclava",
            HOSPITALISE_ITEM_NAME: "Death Book",
            REVIVE_ITEM_NAME: "Life Book",
            JAIL_ITEM_NAME: "Kompromat",
            MEGAPHONE_ITEM_NAME: "Megaphone",
            GANG_CREATION_ITEM_NAME: "Gang Sigil",
            DAILY_CHEST_ITEM_ID: 232,
        },
        hidden: {
            DEATHNOTE_HOSPITAL_TIME_MS: ms("30m"),
            KOMPROMAT_JAIL_TIME_MS: ms("20m"),
        },
    },
    classesConfig: {
        public: { CLASS_NAMES: ["Honoo", "Mizu", "Tsuchi", "Kaze"] },
        hidden: {},
    },
    registrationCodes: {
        public: { REGISTRATION_CODES_DISABLED: true },
        hidden: {},
    },
    questsConfig: {
        public: { QUESTS_DISABLED: false },
        hidden: {
            QUEST_XP_REWARD_MULTIPLIER: 0.25,
            DAILY_QUEST_XP_REWARD_MULTIPLIER: 0.1,
        },
    },
    shopConfig: {
        public: {
            MAX_TRADER_REP: 3,
            SHOP_ITEM_COST_MULTIPLIER: 7,
            LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT: 4,
        },
        hidden: {},
    },
    skillsConfig: {
        public: {
            STAMINA_PER_ENDURANCE_LEVEL: 5,
        },
        hidden: {},
    },
    bountyConfig: {
        public: {
            MIN_BOUNTY: 100,
            BOUNTY_FEE: 0.1,
            BOUNTY_MIN_LEVEL: 5,
            DEFAULT_STAFF_BOUNTY_USER_ID: 5,
            RANDOM_BOUNTY_AMOUNT: 50,
        },
        hidden: {
            RANDOM_BOUNTY_CHANCE: 0.2,
        },
    },
    profileConfig: {
        public: { PROFILE_COMMENT_MAX_LENGTH: 160 },
        hidden: {},
    },
    missionConfig: {
        public: {
            MISSION_TIER_REQ_LEVELS: [4, 10, 15, 20, 25],
            MISSION_TIER_REQ_HOURS: [0, 15, 40, 80, 125],
            MISSION_DURATIONS_HOURS: [2, 4, 8],
        },
        hidden: {},
    },
    shrineConfig: {
        public: {
            SHRINE_DISABLED: false,
            SHRINE_MINIMUM_DONATION: 100,
        },
        hidden: {
            SHRINE_GOAL_CIRCULATING_PERCENT: 0.07,
        },
    },
    auctionConfig: {
        public: {
            ALLOWED_AUCTION_ITEM_TYPES: [
                "weapon",
                "offhand",
                "ranged",
                "shield",
                "head",
                "finger",
                "hands",
                "chest",
                "legs",
                "feet",
                "consumable",
                "junk",
                "special",
                "recipe",
                "crafting",
            ],
            BLACKLISTED_AUCTION_ITEM_TYPES: ["quest"],
            BLACKLISTED_AUCTION_ITEM_IDS: [232, 37, 22, 155, 250, 247, 248, 246, 107, 108, 244],
        },
        hidden: {},
    },
    gangConfig: {
        public: {
            DAILY_ESSENCE_CAP: 100,
        },
        hidden: {
            ELO_K_FACTOR: 32,
            BASE_LIFE_ESSENCE_REWARD: 5,
            BASE_RESPECT_REWARD: 25,
        },
    },
    infirmaryConfig: {
        public: {
            MINOR_COST_PER_LEVEL: 125,
            MODERATE_COST_PER_LEVEL: 250,
            SEVERE_COST_PER_LEVEL: 450,
            COST_PER_HP: 4,
        },
        hidden: {},
    },
};

// Create a mutable copy that will hold the current test configuration
let configs = deepClone(originalConfigs);

// Function to create a flat config from the nested structure
function createFlatConfig(cfg) {
    const flattened = Object.entries(cfg).reduce((acc, [key, value]) => {
        if (key === "version") {
            return acc;
        }
        const section = value;
        Object.assign(acc, section.public, section.hidden);
        return acc;
    }, {});

    flattened.version = cfg.version;
    return flattened;
}

// Function to create a public-only config
function createPublicConfig(cfg) {
    const flattened = Object.entries(cfg).reduce((acc, [key, value]) => {
        if (key === "version") {
            return acc;
        }
        const section = value;
        Object.assign(acc, section.public);
        return acc;
    }, {});

    flattened.version = cfg.version;
    return flattened;
}

// Create initial flat and public configs
let flatConfig = createFlatConfig(configs);
let publicConfig = createPublicConfig(configs);

// Mock version
const configVersion = configs.version;

// Reset function to restore original configs
const resetMock = () => {
    configs = deepClone(originalConfigs);
    flatConfig = createFlatConfig(configs);
    publicConfig = createPublicConfig(configs);
};

// Override function to modify config values for specific tests
const overrideConfig = (path, value) => {
    // Parse the path (e.g., "authConfig.public.REGISTRATION_DISABLED")
    const parts = path.split(".");

    if (parts.length < 2) {
        throw new Error(`Invalid config path: ${path}. Must include at least config section and property.`);
    }

    const [section, subsection, ...propertyPath] = parts;

    // Ensure the section exists
    if (!configs[section]) {
        configs[section] = { public: {}, hidden: {} };
    }

    // Navigate to the right subsection
    if (subsection !== "public" && subsection !== "hidden") {
        // If not specifying public/hidden, assume it's a property and put it in both sections
        const property = [subsection, ...propertyPath].join(".");
        const propParts = property.split(".");
        let currentPublic = configs[section].public;
        let currentHidden = configs[section].hidden;

        // Navigate to the nested property
        for (let i = 0; i < propParts.length - 1; i++) {
            const part = propParts[i];
            if (!currentPublic[part]) currentPublic[part] = {};
            if (!currentHidden[part]) currentHidden[part] = {};
            currentPublic = currentPublic[part];
            currentHidden = currentHidden[part];
        }

        // Set the value
        const finalProp = propParts.at(-1);
        currentPublic[finalProp] = value;
        currentHidden[finalProp] = value;
    } else {
        // Navigate to the nested property in the specified subsection
        let current = configs[section][subsection];
        for (let i = 0; i < propertyPath.length - 1; i++) {
            const part = propertyPath[i];
            if (!current[part]) current[part] = {};
            current = current[part];
        }

        // Set the value
        const finalProp = propertyPath.at(-1);
        current[finalProp] = value;
    }

    // Regenerate the flat config
    flatConfig = createFlatConfig(configs);
    publicConfig = createPublicConfig(configs);
};

// Extract individual configs from the main configs object
const extractConfigs = () => {
    const {
        authConfig,
        frontendConfig,
        battleConfig,
        bankConfig,
        chatConfig,
        craftingConfig,
        casinoConfig,
        userConfig,
        jobsConfig,
        notificationConfig,
        levelGatesConfig,
        leaderboardsConfig,
        roguelikeConfig,
        uniqueItemsConfig,
        classesConfig,
        registrationCodes,
        questsConfig,
        shopConfig,
        skillsConfig,
        bountyConfig,
        profileConfig,
        missionConfig,
        shrineConfig,
        auctionConfig,
        gangConfig,
        infirmaryConfig,
    } = configs;

    return {
        authConfig,
        frontendConfig,
        battleConfig,
        bankConfig,
        chatConfig,
        craftingConfig,
        casinoConfig,
        userConfig,
        jobsConfig,
        notificationConfig,
        levelGatesConfig,
        leaderboardsConfig,
        roguelikeConfig,
        uniqueItemsConfig,
        classesConfig,
        registrationCodes,
        questsConfig,
        shopConfig,
        skillsConfig,
        bountyConfig,
        profileConfig,
        missionConfig,
        shrineConfig,
        auctionConfig,
        gangConfig,
        infirmaryConfig,
    };
};

// Create mocks for all exported variables and functions
const {
    authConfig,
    frontendConfig,
    battleConfig,
    bankConfig,
    chatConfig,
    craftingConfig,
    casinoConfig,
    userConfig,
    jobsConfig,
    notificationConfig,
    levelGatesConfig,
    leaderboardsConfig,
    roguelikeConfig,
    uniqueItemsConfig,
    classesConfig,
    registrationCodes,
    questsConfig,
    shopConfig,
    skillsConfig,
    bountyConfig,
    profileConfig,
    missionConfig,
    shrineConfig,
    auctionConfig,
    gangConfig,
    infirmaryConfig,
} = extractConfigs();

export {
    configVersion,
    authConfig,
    frontendConfig,
    battleConfig,
    bankConfig,
    chatConfig,
    craftingConfig,
    casinoConfig,
    userConfig,
    jobsConfig,
    notificationConfig,
    levelGatesConfig,
    leaderboardsConfig,
    roguelikeConfig,
    uniqueItemsConfig,
    classesConfig,
    registrationCodes,
    questsConfig,
    shopConfig,
    skillsConfig,
    bountyConfig,
    profileConfig,
    missionConfig,
    shrineConfig,
    auctionConfig,
    gangConfig,
    infirmaryConfig,
    configs as gameConfig,
    resetMock,
    overrideConfig,
};

export default flatConfig;
export { publicConfig };
