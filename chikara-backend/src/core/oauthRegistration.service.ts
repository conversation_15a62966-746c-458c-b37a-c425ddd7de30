import { redisClient } from "../config/redisClient.js";
import crypto from "node:crypto";

export interface TempOAuthData {
    tempId: string;
    email: string;
    name?: string;
    image?: string;
    providerId: string;
    accountId: string;
    accessToken?: string;
    refreshToken?: string;
    createdAt: number;
}

export interface OAuthRegistrationData {
    username: string;
    tempId: string;
    code?: string;
}

const TEMP_OAUTH_PREFIX = "oauth-temp:";
const TEMP_OAUTH_TTL = 3600; // 1 hour

export const OAuthRegistrationService = {
    /**
     * Store temporary OAuth data for incomplete registration
     */
    async storeTempOAuthData(data: Omit<TempOAuthData, "tempId" | "createdAt">): Promise<string> {
        const tempId = crypto.randomUUID();
        const tempData: TempOAuthData = {
            ...data,
            tempId,
            createdAt: Date.now(),
        };

        await redisClient.set(`${TEMP_OAUTH_PREFIX}${tempId}`, JSON.stringify(tempData), { EX: TEMP_OAUTH_TTL });

        return tempId;
    },

    /**
     * Retrieve temporary OAuth data
     */
    async getTempOAuthData(tempId: string): Promise<TempOAuthData | null> {
        const data = await redisClient.get(`${TEMP_OAUTH_PREFIX}${tempId}`);
        if (!data) return null;

        try {
            return JSON.parse(data) as TempOAuthData;
        } catch {
            return null;
        }
    },

    /**
     * Delete temporary OAuth data
     */
    async deleteTempOAuthData(tempId: string): Promise<void> {
        await redisClient.del(`${TEMP_OAUTH_PREFIX}${tempId}`);
    },

    /**
     * Check if a user already exists with the given email
     */
    async checkUserExists(email: string): Promise<boolean> {
        const { db } = await import("../lib/db.js");
        const user = await db.user.findFirst({
            where: { email },
        });
        return !!user;
    },

    /**
     * Create user from temporary OAuth data
     */
    async createUserFromTempData(
        tempData: TempOAuthData,
        username: string
    ): Promise<{ success: boolean; userId?: number; error?: string }> {
        const { db } = await import("../lib/db.js");
        const { auth } = await import("../lib/auth.js");
        const { fromNodeHeaders } = await import("better-auth/node");
        const { AuthRepository } = await import("../features/auth/auth.repository.js");

        try {
            // Create user through better-auth
            const newUser = await auth.api.signUpEmail({
                headers: fromNodeHeaders({}),
                body: {
                    name: username,
                    username: username,
                    email: tempData.email,
                    password: crypto.randomBytes(32).toString("hex"), // Random password for OAuth users
                    emailVerified: true, // OAuth emails are pre-verified
                },
            });

            if (!newUser) {
                return { success: false, error: "Failed to create user" };
            }

            const userId = Number.parseInt(newUser.user.id);

            // Create OAuth account link
            await db.account.create({
                data: {
                    userId: newUser.user.id,
                    providerId: tempData.providerId,
                    accountId: tempData.accountId,
                    accessToken: tempData.accessToken,
                    refreshToken: tempData.refreshToken,
                },
            });

            // Add user data (class assignment, etc.)
            const assignedClass = await AuthRepository.findLeastPopulatedClass();
            await db.user.update({
                where: { id: userId },
                data: {
                    class: assignedClass,
                },
            });

            return { success: true, userId };
        } catch (error) {
            console.error("Error creating user from OAuth data:", error);
            return { success: false, error: "Failed to create user" };
        }
    },
};
