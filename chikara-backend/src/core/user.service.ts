import gameConfig from "../config/gameConfig.js";
import * as InventoryService from "./inventory.service.js";
import * as NotificationService from "./notification.service.js";
import { referralRewards } from "../data/referralRewards.js";
import * as ChatHelper from "../features/chat/chat.helpers.js";
import * as ShrineHelper from "../features/shrine/shrine.helpers.js";
import { logAction } from "../lib/actionLogger.js";
import { UserModel, db } from "../lib/db.js";
import { expiryQueue } from "../queues/expiryValues/queue.js";
import { NotificationTypes } from "../types/notification.js";
import { LogErrorStack, logger } from "../utils/log.js";
import { Prisma } from "@prisma/client";
import * as UserRepository from "../repositories/user.repository.js";

export const updateUser = async (userId: UserModel["id"], updateData: Prisma.userUpdateInput) => {
    return await db.user.update({
        where: { id: userId },
        data: updateData,
    });
};

export const getXpForNextLevel = (user: UserModel) => {
    const level = user.level || 1;

    if (level >= 35) {
        return Math.floor(level * (gameConfig.XP_TO_LEVEL_MULTIPLIER * (level / 8)));
    }

    if (level >= 30) {
        return Math.floor(level * (gameConfig.XP_TO_LEVEL_MULTIPLIER * (level / 12)));
    }

    if (level >= 25) {
        return Math.floor(level * (gameConfig.XP_TO_LEVEL_MULTIPLIER * (level / 16)));
    }

    return Math.floor(level * gameConfig.XP_TO_LEVEL_MULTIPLIER);
};

/**
 * Process referral rewards when a user reaches specific level milestones
 */
export const processReferralRewards = async (user: UserModel, newLevel: number) => {
    // Check if user has a referrer
    if (!user.referrerId) return;

    // Check if the new level is a reward tier
    const rewardTier = `level${newLevel}` as keyof typeof referralRewards;
    if (!(rewardTier in referralRewards)) return;

    const rewards = referralRewards[rewardTier].rewards;

    // Get referrer user
    const referrer = await UserRepository.getUserById(user.referrerId);
    if (!referrer) return;

    // Award items to referrer
    for (const reward of rewards) {
        await InventoryService.AddItemToUser({
            userId: referrer.id,
            itemId: reward.itemId,
            amount: reward.quantity,
            isTradeable: true,
        });
    }

    // Send notification to referrer
    await NotificationService.NotifyUser(referrer.id, NotificationTypes.referral_reward, {
        title: "Referral Reward",
        message: `You received rewards because ${user.username} reached level ${newLevel}!`,
        items: rewards,
    });

    // Log the action
    logAction({
        action: "REFERRAL_REWARD",
        userId: referrer.id,
        info: {
            referreeId: user.id,
            level: newLevel,
            rewards,
        },
    });
};

/**
 * Process level ups and return the updated user state
 */
export const processLevelUps = (
    user: UserModel,
    currentState: {
        xp: number;
        level: number;
        talentPoints: number;
        health: number;
        currentHealth: number;
    }
) => {
    let nextLevelXpReq = getXpForNextLevel({ ...user, level: currentState.level });
    const updatedState = { ...currentState };

    while (updatedState.xp >= nextLevelXpReq && updatedState.level < gameConfig.MAX_LEVEL_CAP) {
        // Level up
        updatedState.level++;

        // Apply level-up benefits
        if (updatedState.level >= gameConfig.TALENTS_LEVEL_GATE) {
            updatedState.talentPoints++;
        }
        updatedState.health += 50;
        updatedState.currentHealth += 50;

        // Update XP and calculate next level requirement
        updatedState.xp -= nextLevelXpReq;
        nextLevelXpReq = getXpForNextLevel({ ...user, level: updatedState.level });

        // Send notifications
        NotificationService.NotifyUser(user.id, NotificationTypes.levelup, { newLevel: updatedState.level });

        // Process referral rewards
        processReferralRewards(user, updatedState.level).catch((error) => {
            LogErrorStack({ message: "Error processing referral rewards:", error });
        });

        // Handle max level cap reached
        if (updatedState.level === gameConfig.MAX_LEVEL_CAP) {
            ChatHelper.SendAnnouncementMessage(
                "levelCapReached",
                JSON.stringify({ username: user.username, id: user.id })
            );
        }

        // Log the level-up event
        logAction({
            action: "LEVEL_UP",
            userId: user.id,
            info: {
                newLevel: updatedState.level,
            },
        });
    }

    return updatedState;
};

export const AddXPToUser = async (user: UserModel, xp: number) => {
    // Apply experience multipliers
    const shrineBuffActive = await ShrineHelper.dailyBuffIsActive("exp", user.id);
    // Calculate adjusted XP
    let adjustedXp = xp;
    if (shrineBuffActive) {
        adjustedXp *= shrineBuffActive;
    }
    adjustedXp = Math.round(adjustedXp);

    // Store initial values
    const initialState = {
        xp: user.xp,
        level: user.level,
        talentPoints: user.talentPoints,
        health: user.health,
        currentHealth: user.currentHealth,
    };

    // Setup current values to track changes
    const currentState = {
        xp: initialState.xp + adjustedXp,
        level: initialState.level,
        talentPoints: initialState.talentPoints,
        health: initialState.health,
        currentHealth: initialState.currentHealth,
    };

    // Early return if max level reached
    if (currentState.level >= gameConfig.MAX_LEVEL_CAP) {
        await updateUser(user.id, { xp: currentState.xp });
        return adjustedXp;
    }

    // Process level-ups
    const updatedState = processLevelUps(user, currentState);

    // Prepare database updates
    const updates: Prisma.userUpdateInput = {
        xp: updatedState.xp,
    };

    // Only add fields that changed to the update
    if (updatedState.level !== initialState.level) {
        updates.level = updatedState.level;
        updates.health = updatedState.health;
        updates.currentHealth = updatedState.currentHealth;
    }

    if (updatedState.talentPoints !== initialState.talentPoints) {
        updates.talentPoints = updatedState.talentPoints;
    }

    // Save changes and return the awarded XP
    await updateUser(user.id, updates);
    return adjustedXp;
};

export const JailUser = async (
    userId: number,
    duration: number,
    reason: string,
    notificationDetails?: { notificationType?: NotificationTypes; attacked?: number }
) => {
    const jailEndTime = Date.now() + duration;

    const user = await db.user.update({
        where: { id: userId },
        data: { jailedUntil: BigInt(jailEndTime), jailReason: reason },
    });

    if (!user.jailedUntil) {
        logger.warn(`User ${userId} was not jailed correctly`);
        return user;
    }

    const expiryTime = user.jailedUntil.toString();

    await expiryQueue.add(
        "jail-expiry",
        {
            userId: userId,
            action: "jailedUntil",
            expiryTime,
        },
        {
            delay: duration,
            jobId: `jail-${userId}-${expiryTime}`,
            attempts: 3,
            removeOnComplete: true,
            removeOnFail: false,
        }
    );

    if (notificationDetails) {
        NotificationService.NotifyUser(userId, notificationDetails.notificationType || NotificationTypes.jail, {
            reason,
            jailedUntil: jailEndTime,
            ...notificationDetails,
        });
    }

    return user;
};
