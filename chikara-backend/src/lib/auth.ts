import { redisClient } from "../config/redisClient.js";
import { getAllowedOrigins } from "../config/cors.js";
import { logAction } from "./actionLogger.js";
import { db } from "./db.js";
import { PasswordResetEmail } from "../utils/email.js";
import { betterAuth } from "better-auth";
// import { emailHarmony } from "better-auth-harmony";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { createAuthMiddleware } from "better-auth/api";
import { toNodeHandler } from "better-auth/node";
import { admin, username, openAPI } from "better-auth/plugins";
import type { Express } from "express";
import { containsBlacklistedWords } from "../utils/contentFilter.js";

// https://betterauth.apidocumentation.com/

const prisma = db;

const appBaseUrl = process.env.APP_BASE_URL || "https://api.battleacademy.io";

export const auth = betterAuth({
    appName: "Chikara Academy",
    baseURL: appBaseUrl,
    basePath: "/auth",
    plugins: [
        // TODO: Reenable once validator.js ESM compat is fixed
        // emailHarmony(),
        // openAPI(),
        username({
            minUsernameLength: 4,
            maxUsernameLength: 17,
            usernameValidator: (name) => {
                if (name === "admin" || containsBlacklistedWords(name)) {
                    return false;
                }
                return true;
            },
        }),
        admin({
            defaultRole: "student",
            schema: {
                user: {
                    fields: {
                        role: "userType",
                    },
                },
            },
        }),
    ],
    socialProviders: {
        discord: {
            clientId: process.env.DISCORD_CLIENT_ID as string,
            clientSecret: process.env.DISCORD_CLIENT_SECRET as string,
        },
        google: {
            clientId: process.env.GOOGLE_CLIENT_ID as string,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
        },
    },
    emailAndPassword: {
        enabled: true,
        autoSignIn: true,
        sendResetPassword: async ({ user, url, token }) => {
            await PasswordResetEmail({
                email: user.email,
                resetUrl: url,
                resetToken: token,
            });
        },
    },
    user: {
        fields: {
            name: "username",
            image: "avatar",
        },
        changeEmail: {
            enabled: true,
        },
    },
    database: prismaAdapter(prisma, {
        provider: "mysql",
    }),
    session: {
        expiresIn: 60 * 60 * 24 * 365, // 1 year (every 1 year the session expires)
        updateAge: 60 * 60 * 24, // 1 day (every 1 day the session expiration is updated)
    },
    secondaryStorage: {
        get: async (key) => (await redisClient.get(`chikara-backend-session:${key}`)) as string,
        set: async (key, value, ttl) => {
            await (ttl
                ? redisClient.set(`chikara-backend-session:${key}`, value, { EX: ttl })
                : redisClient.set(`chikara-backend-session:${key}`, value));
        },
        delete: async (key) => {
            await redisClient.del(`chikara-backend-session:${key}`);
            return null;
        },
    },
    databaseHooks: {
        account: {
            create: {
                // eslint-disable-next-line require-await
                before: async (account) => {
                    return {
                        data: {
                            ...account,
                            accountId: String(account.accountId),
                        },
                    };
                },
                after: async (account) => {
                    if (account.providerId === "discord" || account.providerId === "google") {
                        // Store OAuth account data temporarily for potential incomplete registrations
                        const tempAccountData = {
                            providerId: account.providerId,
                            accountId: account.accountId,
                            accessToken: account.accessToken,
                            refreshToken: account.refreshToken,
                            accessTokenExpiresAt: account.accessTokenExpiresAt,
                            createdAt: Date.now(),
                        };

                        await redisClient.set(
                            `oauth-account:${account.userId}`,
                            JSON.stringify(tempAccountData),
                            { EX: 3600 } // 1 hour expiry
                        );
                    }
                },
            },
        },
    },
    hooks: {
        before: createAuthMiddleware(async (ctx) => {
            // Log logout before the session is destroyed
            if (ctx.path === "/sign-out") {
                // Get the current session from cookies to log the logout
                const sessionToken = ctx.getCookie("better-auth.session_token");
                if (sessionToken && ctx.headers) {
                    const session = await auth.api.getSession({
                        headers: ctx.headers,
                    });

                    if (session?.session) {
                        await logAction({
                            action: "LOGOUT",
                            userId: session.session.userId,
                            session: session.session,
                        });
                    }
                }
            }
        }),
        after: createAuthMiddleware(async (ctx) => {
            if (ctx.path.startsWith("/sign-in")) {
                const newSession = ctx.context.newSession;
                if (newSession) {
                    await logAction({
                        action: "LOGIN",
                        userId: newSession.session.userId,
                        session: newSession.session,
                    });
                }
            }
        }),
    },
    advanced: {
        database: { useNumberId: true, generateId: false },
        defaultCookieAttributes: {
            sameSite: "none",
            secure: true,
        },
        crossSubDomainCookies: {
            enabled: process.env.NODE_ENV !== "development",
            domain: "battleacademy.io", // Optional. Defaults to the base url domain
        },
    },
    trustedOrigins: getAllowedOrigins(true) as string[],
    crossSubDomain: true,
});

export type Session = typeof auth.$Infer.Session.session;

export const setupAuthRoutes = (app: Express) => {
    app.all("/auth/*name", toNodeHandler(auth));
};
