import gameConfig from "../../config/gameConfig.js";
import { Prisma } from "@prisma/client";

export default Prisma.defineExtension((client) => {
    return client.$extends({
        result: {
            shop_listing: {
                cost: {
                    needs: {
                        id: true,
                        customCost: true,
                        itemId: true,
                    },
                    compute(shop_listing) {
                        // If customCost is set, use that instead
                        if (shop_listing.customCost !== null) {
                            return shop_listing.customCost;
                        }

                        // For item-based cost calculation, we need to include the item in the query
                        // This will only work if the query includes { item: true } or { item: { select: { cashValue: true } } }
                        // @ts-expect-error - we know this might not exist if item isn't included in the query
                        const item = shop_listing.item;
                        if (item && item.cashValue !== null) {
                            return Math.round(item.cashValue * gameConfig.SHOP_ITEM_COST_MULTIPLIER);
                        }

                        return null;
                    },
                },
            },
        },
    });
});
