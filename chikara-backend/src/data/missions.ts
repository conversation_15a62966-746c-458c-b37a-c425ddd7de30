import gameConfig from "../config/gameConfig.js";

interface CashAmount {
    minCash: number;
    maxCash: number;
}

interface ExpAmount {
    minExp: number;
    maxExp: number;
}

interface MissionTier {
    level: number;
    reqHours: number;
    cashAmounts: CashAmount[];
    expAmounts: ExpAmount[];
    itemRewardIds: number[];
}

interface Mission {
    name: string;
    description: string;
}

export const missionTiers: Record<number, MissionTier> = {
    1: {
        level: gameConfig.MISSION_TIER_REQ_LEVELS[0],
        reqHours: 0,
        cashAmounts: [
            { minCash: 50, maxCash: 100 },
            { minCash: 125, maxCash: 250 },
            { minCash: 310, maxCash: 625 },
        ],
        expAmounts: [
            { minExp: 100, maxExp: 200 },
            { minExp: 250, maxExp: 500 },
            { minExp: 625, maxExp: 1250 },
        ],
        itemRewardIds: [197, 189, 125, 187],
    },
    2: {
        level: gameConfig.MISSION_TIER_REQ_LEVELS[1],
        reqHours: gameConfig.MISSION_TIER_REQ_HOURS[1],
        cashAmounts: [
            { minCash: 80, maxCash: 160 },
            { minCash: 200, maxCash: 400 },
            { minCash: 500, maxCash: 1000 },
        ],
        expAmounts: [
            { minExp: 330, maxExp: 660 },
            { minExp: 825, maxExp: 1650 },
            { minExp: 2050, maxExp: 4125 },
        ],
        itemRewardIds: [126, 184, 185],
    },
    3: {
        level: gameConfig.MISSION_TIER_REQ_LEVELS[2],
        reqHours: gameConfig.MISSION_TIER_REQ_HOURS[2],
        cashAmounts: [
            { minCash: 140, maxCash: 280 },
            { minCash: 350, maxCash: 700 },
            { minCash: 875, maxCash: 1750 },
        ],
        expAmounts: [
            { minExp: 500, maxExp: 1000 },
            { minExp: 1250, maxExp: 2500 },
            { minExp: 3125, maxExp: 6250 },
        ],
        itemRewardIds: [128, 198, 186, 210, 211],
    },
    4: {
        level: gameConfig.MISSION_TIER_REQ_LEVELS[3],
        reqHours: gameConfig.MISSION_TIER_REQ_HOURS[3],
        cashAmounts: [
            { minCash: 200, maxCash: 400 },
            { minCash: 500, maxCash: 1000 },
            { minCash: 1250, maxCash: 2500 },
        ],
        expAmounts: [
            { minExp: 670, maxExp: 1340 },
            { minExp: 1675, maxExp: 3350 },
            { minExp: 4175, maxExp: 8375 },
        ],
        itemRewardIds: [129, 199, 202, 201, 204],
    },
    5: {
        level: gameConfig.MISSION_TIER_REQ_LEVELS[4],
        reqHours: gameConfig.MISSION_TIER_REQ_HOURS[4],
        cashAmounts: [
            { minCash: 250, maxCash: 500 },
            { minCash: 625, maxCash: 1250 },
            { minCash: 1550, maxCash: 3125 },
        ],
        expAmounts: [
            { minExp: 800, maxExp: 1600 },
            { minExp: 2000, maxExp: 4000 },
            { minExp: 5000, maxExp: 10_000 },
        ],
        itemRewardIds: [130, 204, 190],
    },
} as const;

const tier1Missions: Mission[] = [
    {
        name: "Courier Run",
        description:
            "Deliver critical documents across the academy grounds, evading novice thieves and gathering essential intelligence on your way.",
    },
    {
        name: "Night Watch",
        description:
            "Patrol the quieter districts of the academy after dark, ensuring the safety of students and faculty while honing your alertness.",
    },
    {
        name: "Data Heist",
        description:
            "Infiltrate a rival school's server to secure competitive data, requiring stealth and basic decryption skills.",
    },
    {
        name: "Scout Patrol",
        description:
            "Perform a perimeter check around the academy, identifying any security breaches and reporting back to command.",
    },
    {
        name: "Sabotage Cleanup",
        description:
            "Reverse the effects of a recent prank by rival students that has left several academy systems malfunctioning.",
    },
    {
        name: "Archival Aid",
        description:
            "Assist in the digitization of ancient documents in the academy library, protecting them from potential theft.",
    },
    {
        name: "Coded Messages",
        description:
            "Decode a series of cryptic messages appearing on academy screens, believed to be the work of a clever rival.",
    },
    {
        name: "Tech Salvage",
        description:
            "Recover and repurpose discarded technological components found around the academy grounds for educational use.",
    },
    {
        name: "Herbal Harvest",
        description:
            "Collect rare medicinal plants from the academy's botanical gardens for use in first aid training exercises.",
    },
] as const;

const tier2Missions: Mission[] = [
    {
        name: "Supply Guard",
        description:
            "Protect a shipment of rare materials from petty criminals as it makes its way to the academy's tech lab.",
    },
    {
        name: "Urban Recon",
        description:
            "Map out gang territories within the city, gathering detailed intelligence without drawing attention to yourself.",
    },
    {
        name: "Blackout Prevention",
        description:
            "Thwart a planned sabotage on the city's power grid, testing your problem-solving and crisis management skills.",
    },
    {
        name: "Electronic Sweep",
        description:
            "Conduct a thorough sweep for electronic bugs in the academy's main conference hall ahead of a top-secret meeting.",
    },
    {
        name: "Smuggling Stop",
        description:
            "Intercept a smuggling attempt on the academy grounds and secure stolen artifacts destined for the black market.",
    },
    {
        name: "Signal Jammer",
        description: "Locate and disable illegal signal jammers disrupting communications within the campus area.",
    },
    {
        name: "Urban Tracking",
        description: "Track a suspect through crowded city environments using newly developed surveillance technology.",
    },
    {
        name: "Encryption Challenge",
        description:
            "Encrypt sensitive academy data using a new algorithm, then test its resilience against simulated attacks.",
    },
    {
        name: "Hazardous Material Drill",
        description:
            "Conduct a drill to safely handle and dispose of hazardous materials found in a compromised lab environment.",
    },
] as const;

const tier3Missions: Mission[] = [
    {
        name: "Drone Interception",
        description:
            "Disable unauthorized drones over a sensitive area using advanced electronic gadgets and quick reflexes.",
    },
    {
        name: "Chemical Recovery",
        description:
            "Retrieve stolen chemical compounds from a secured facility, requiring intermediate combat and negotiation skills.",
    },
    {
        name: "Hostage Rescue",
        description:
            "Execute a carefully planned rescue operation in a high-stakes hostage situation, demanding precision and tactical expertise.",
    },
    {
        name: "VIP Escort",
        description:
            "Provide security for a visiting dignitary as they tour the academy, preventing any assassination attempts.",
    },
    {
        name: "Weapons Test",
        description:
            "Test new defensive technologies in a controlled environment and gather data under simulated attack conditions.",
    },
    {
        name: "Artifact Recovery",
        description:
            "Retrieve a stolen historical artifact from a heavily guarded location using advanced stealth techniques.",
    },
    {
        name: "Cyber Forensics",
        description:
            "Conduct a cyber forensic investigation to track down a hacker who has infiltrated the academy's network.",
    },
    {
        name: "Aerial Reconnaissance",
        description:
            "Use drones to perform aerial surveillance of a large district suspected of harboring criminal activities.",
    },
    {
        name: "Negotiator's Test",
        description:
            "Engage in a high-stakes negotiation with simulated hostile parties to secure the release of hostages.",
    },
] as const;

const tier4Missions: Mission[] = [
    {
        name: "Network Defense",
        description:
            "Defend against a sophisticated cyber-attack targeting the academy's archives, using your expert hacking abilities.",
    },
    {
        name: "Undercover Operation",
        description:
            "Infiltrate a notorious underworld organization, gathering crucial evidence while maintaining your cover.",
    },
    {
        name: "Riot Suppression",
        description:
            "Lead a tactical squad to quell a violent riot at a critical infrastructure site, testing your leadership and strategic combat skills.",
    },
    {
        name: "Counter-Intelligence",
        description:
            "Uncover and neutralize a spy network operating within the academy, using counter-surveillance methods.",
    },
    {
        name: "Disaster Simulation",
        description:
            "Lead a team through a complex disaster simulation, making critical decisions to minimize simulated casualties and infrastructure damage.",
    },
    {
        name: "Enemy Base Infiltration",
        description:
            "Infiltrate an enemy base and gather crucial intelligence on their operations and leadership, avoiding detection at all costs.",
    },
    {
        name: "Wartime Logistics",
        description:
            "Oversee the logistics of a simulated wartime supply chain, ensuring resources are allocated efficiently under pressure.",
    },
    {
        name: "Covert Insertion",
        description:
            "Execute a covert operation to insert agents into a hostile territory without alerting the enemy forces.",
    },
    {
        name: "Advanced Decryption",
        description:
            "Break into a complex encryption system that protects the blueprints of a revolutionary new technology.",
    },
] as const;

const tier5Missions: Mission[] = [
    {
        name: "Technological Espionage",
        description:
            "Steal advanced technological blueprints from a high-security corporate tower, requiring mastery of stealth and tech skills.",
    },
    {
        name: "Biohazard Containment",
        description: "Contain a dangerous outbreak in a biotech facility, navigating biohazards and hostile entities.",
    },
    {
        name: "Apocalypse Prevention",
        description:
            "Dismantle a doomsday device in a race against time, involving every skill and resource you have acquired.",
    },
    {
        name: "Quantum Defense",
        description:
            "Defend against a quantum computing attack that threatens to decrypt sensitive academy data, employing cutting-edge technology.",
    },
    {
        name: "Global Threat Neutralization",
        description:
            "Travel to a remote location to neutralize a global threat posed by rogue elements using stolen academy tech.",
    },
    {
        name: "Temporal Recovery",
        description:
            "Engage in a high-risk mission to recover experimental technology that has caused temporal anomalies around the academy.",
    },
    {
        name: "Dimensional Shift",
        description:
            "Investigate anomalies causing dimensional shifts at the academy, using advanced quantum mechanics.",
    },
    {
        name: "Extinction Event",
        description:
            "Prevent a simulated extinction-level event by coordinating global defenses and scientific research.",
    },
    {
        name: "Ultimate Simulation",
        description:
            "Lead a series of connected simulations designed to test the limits of academy training across all disciplines.",
    },
] as const;

interface MissionsExport {
    tier1Missions: Mission[];
    tier2Missions: Mission[];
    tier3Missions: Mission[];
    tier4Missions: Mission[];
    tier5Missions: Mission[];
}

const missions: MissionsExport = {
    tier1Missions,
    tier2Missions,
    tier3Missions,
    tier4Missions,
    tier5Missions,
} as const;

export default missions;
