import gameConfig from "../../config/gameConfig.js";
import * as AchievementService from "../../core/achievement.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as UserService from "../../core/user.service.js";
import * as QuestRepository from "../../repositories/quest.repository.js";
import { QuestModel, QuestObjectiveProgressModel, UserModel } from "../../lib/db.js";
import { QuestObjectiveTypes } from "../../types/quest.js";
import { LogErrorStack } from "../../utils/log.js";
import { QuestProgressStatus, QuestRewardType } from "@prisma/client";

type ObjectiveProgressWithObjective = QuestObjectiveProgressModel<{ include: { quest_objective: true } }>;

export async function UserCanStartQuest(currentUser: UserModel, quest: QuestModel) {
    const userQuestProgress = await QuestRepository.getUserQuestProgress(currentUser.id, quest.id);
    if (userQuestProgress) {
        return { error: "User has already started this quest", statusCode: 400 };
    }

    return await checkQuestRequirements(currentUser, quest);
}

export async function checkQuestRequirements(currentUser: UserModel, quest: QuestModel) {
    if (quest.disabled) {
        return { error: "Quest is disabled", statusCode: 400 };
    }

    if (currentUser.level < quest.levelReq) {
        return { error: "User does not meet level requirement", statusCode: 400 };
    }

    if (quest.requiredQuestId) {
        const prerequisiteProgress = await QuestRepository.getUserQuestProgress(currentUser.id, quest.requiredQuestId);
        if (!prerequisiteProgress || prerequisiteProgress.questStatus !== QuestProgressStatus.complete) {
            return { error: "Prerequisite quest not completed", statusCode: 400 };
        }
    }

    return { success: true };
}

export const updateQuestObjectiveCount = async (
    questObjectiveProgress: ObjectiveProgressWithObjective[] | ObjectiveProgressWithObjective,
    amount = 1
) => {
    if (!questObjectiveProgress) {
        return;
    }

    if (Array.isArray(questObjectiveProgress) && questObjectiveProgress.length === 0) {
        return;
    }

    if (!Array.isArray(questObjectiveProgress)) {
        questObjectiveProgress = [questObjectiveProgress];
    }

    for (const objProgress of questObjectiveProgress) {
        const objective = objProgress.quest_objective;

        // Skip if objective is already complete
        if (objProgress.status === QuestProgressStatus.complete) {
            continue;
        }

        if (objective.quantity == null || objective.quantity <= 0 || objProgress.count >= objective.quantity) {
            continue;
        }

        if (objProgress.count + amount > objective.quantity) {
            amount = objective.quantity - objProgress.count;
        }

        await QuestRepository.updateQuestObjectiveProgress(objProgress.id, {
            count: { increment: amount },
        });

        // Check if the objective is complete after incrementing the count
        const objectiveComplete = await checkObjectiveComplete(objProgress.id);

        // If the objective was completed, check if the quest is ready to complete
        if (objectiveComplete && objective.questId) {
            await checkQuestReadyToComplete(objProgress.userId, objective.questId);
        }
    }
};

/**
 * Checks if all required objectives for a quest are complete and updates quest status if necessary
 */
export const checkQuestReadyToComplete = async (userId: number, questId: number): Promise<boolean> => {
    // Get the quest progress
    const questProgress = await QuestRepository.getUserQuestProgress(userId, questId);

    if (!questProgress || questProgress.questStatus !== QuestProgressStatus.in_progress) {
        return false;
    }

    // Get all required objectives for this quest
    const objectiveProgress = await QuestRepository.findRequiredUserQuestObjectiveProgressForQuest(userId, questId);

    if (objectiveProgress.length === 0) {
        return false;
    }

    const requiredObjectives = objectiveProgress.filter(
        (progress) => progress.quest_objective.objectiveType !== QuestObjectiveTypes.ACQUIRE_ITEM
    );

    // Check if all required objectives are complete
    const allComplete = requiredObjectives.every((progress) => progress.status === QuestProgressStatus.complete);

    if (allComplete) {
        const optionalObjectives = objectiveProgress.filter(
            (progress) =>
                progress.quest_objective.objectiveType === QuestObjectiveTypes.ACQUIRE_ITEM ||
                progress.quest_objective.objectiveType === QuestObjectiveTypes.GATHER_RESOURCES
        );

        for (const objective of optionalObjectives) {
            await QuestRepository.updateQuestObjectiveProgress(objective.id, {
                count: objective.quest_objective.quantity || 0,
                status: QuestProgressStatus.complete,
            });
        }

        // Update the quest status to ready_to_complete
        await QuestRepository.updateQuestProgress(questProgress.id, {
            questStatus: QuestProgressStatus.ready_to_complete,
        });

        return true;
    }

    return false;
};

/**
 * Checks if a quest objective is complete and updates its status accordingly
 */
export const checkObjectiveComplete = async (
    objectiveProgressId: number,
    type: "quantity" | "target" = "quantity"
): Promise<boolean> => {
    // Get the latest objective progress data
    const objectiveProgress = await QuestRepository.getQuestObjectiveProgressById(objectiveProgressId);

    if (!objectiveProgress || objectiveProgress.status === QuestProgressStatus.complete) {
        return false;
    }

    const objective = await QuestRepository.getQuestObjectiveById(objectiveProgress.questObjectiveId);

    if (!objective || objective[type] == null) {
        return false;
    }

    // Check if the count meets or exceeds the required quantity
    if (objectiveProgress.count >= objective[type]) {
        // Update the objective status to complete
        await QuestRepository.updateQuestObjectiveProgress(objectiveProgressId, {
            status: QuestProgressStatus.complete,
        });

        // Check if the quest is now ready to complete
        if (objective.questId) {
            await checkQuestReadyToComplete(objectiveProgress.userId, objective.questId);
        }

        return true;
    }

    return false;
};

export const AddTraderRep = async (userId: number, shopId: number, rep: number) => {
    const traderRep = await QuestRepository.findTraderRep(userId, shopId);

    if (traderRep) {
        await QuestRepository.saveTraderRep({
            ...traderRep,
            reputationLevel: Math.min(traderRep.reputationLevel + rep, gameConfig.MAX_TRADER_REP),
        });
        return;
    }

    await QuestRepository.createTraderRep(userId, shopId, rep);
};

export const ApplyQuestCompletion = async (
    quest: QuestModel<{ include: { quest_reward: { include: { item: true } } } }>,
    user: UserModel
) => {
    if ("quest_reward" in quest && Array.isArray(quest.quest_reward)) {
        const updates: { cash?: number; talentPoints?: number; gangCreds?: number; classPoints?: number } = {};

        if (quest.cashReward && quest.cashReward > 0) {
            updates.cash = (updates.cash || user.cash || 0) + quest.cashReward;
        }

        if (quest.xpReward && quest.xpReward > 0) {
            await UserService.AddXPToUser(user, quest.xpReward);
        }

        if (quest.repReward && quest.repReward > 0) {
            if ("shopId" in quest && quest.shopId) {
                await AddTraderRep(user.id, quest.shopId, quest.repReward);
            } else {
                LogErrorStack({
                    message: `Quest ${quest.id} has REP reward without shopId.`,
                    error: new Error(`Quest ${quest.id} has REP reward without shopId.`),
                });
            }
        }

        for (const reward of quest.quest_reward) {
            switch (reward.rewardType) {
                case QuestRewardType.TALENT_POINTS: {
                    updates.talentPoints = (updates.talentPoints || user.talentPoints || 0) + reward.quantity;
                    break;
                }
                case QuestRewardType.ITEM: {
                    if (reward.itemId) {
                        await InventoryService.AddItemToUser({
                            userId: user.id,
                            itemId: reward.itemId,
                            amount: reward.quantity,
                            isTradeable: false,
                        });
                    } else {
                        LogErrorStack({
                            message: `Quest ${quest.id} has ITEM reward without itemId.`,
                            error: new Error(`Quest ${quest.id} has ITEM reward without itemId.`),
                        });
                    }
                    break;
                }
                case QuestRewardType.GANG_CREDS: {
                    updates.gangCreds = (updates.gangCreds || user.gangCreds || 0) + reward.quantity;
                    break;
                }
                case QuestRewardType.CLASS_POINTS: {
                    updates.classPoints = (updates.classPoints || user.classPoints || 0) + reward.quantity;
                    break;
                }

                default: {
                    LogErrorStack({
                        message: `Unknown quest reward type: ${reward.rewardType} for quest ${quest.id}`,
                        error: new Error(`Unknown quest reward type: ${reward.rewardType} for quest ${quest.id}`),
                    });
                }
            }
        }

        if (Object.keys(updates).length > 0) {
            await UserService.updateUser(user.id, updates);
        }
    } else {
        LogErrorStack({
            message: `Quest ${quest.id} has no quest_reward array.`,
            error: new Error(`Quest ${quest.id} has no quest_reward array.`),
        });
    }

    await AchievementService.UpdateUserAchievement(user.id, "questsCompleted");
};

/**
 * Updates the state of an DELIVER_ITEM objective to complete if the required quantity is met
 */
export const CompleteItemHandInObjective = async (objectiveProgressId: number, itemCount: number): Promise<boolean> => {
    const objectiveProgress = await QuestRepository.getQuestObjectiveProgressById(objectiveProgressId);

    if (!objectiveProgress || objectiveProgress.status !== QuestProgressStatus.in_progress) {
        return false;
    }

    const objective = await QuestRepository.getQuestObjectiveById(objectiveProgress.questObjectiveId);

    if (!objective || objective.objectiveType !== QuestObjectiveTypes.DELIVER_ITEM || !objective.quantity) {
        return false;
    }

    // Update the objective status to complete
    await QuestRepository.updateQuestObjectiveProgress(objectiveProgressId, {
        count: itemCount,
        status: QuestProgressStatus.complete,
    });

    // Check if the quest is now ready to complete
    if (objective.questId) {
        await checkQuestReadyToComplete(objectiveProgress.userId, objective.questId);
    }

    return true;
};
