import { IncomingHttpHeaders } from "node:http";
import gameConfig from "../../config/gameConfig.js";
import * as AuthRepository from "../../repositories/auth.repository.js";
import { usernameSchema } from "./auth.validation.js";
import * as <PERSON><PERSON><PERSON><PERSON><PERSON> from "../chat/chat.helpers.js";
import { logAction } from "../../lib/actionLogger.js";
import { auth } from "../../lib/auth.js";
import { db } from "../../lib/db.js";
import { containsBlacklistedWords, getRejectedNameError } from "../../utils/contentFilter.js";
import { LogErrorStack } from "../../utils/log.js";
import type { registration_code as RegistrationCodeType } from "@prisma/client";
import { fromNodeHeaders } from "better-auth/node";
import * as UserRepository from "../../repositories/user.repository.js";

const validateUsername = async (username: string) => {
    const validateName = usernameSchema.safeParse(username);

    if (validateName.success === false) {
        return { success: false, message: "Invalid username" };
    }

    if (containsBlacklistedWords(username)) {
        return { success: false, message: getRejectedNameError() };
    }

    const user = await UserRepository.findUserByUsername(username);

    if (user) {
        return { success: false, message: "Username already exists" };
    }

    return { success: true };
};

export const checkUsernameAvailable = async (username: string) => {
    if (!username) {
        return { error: "Username is required" };
    }

    const validationResult = await validateUsername(username);
    if (!validationResult.success) {
        return { error: validationResult.message };
    }
    return { success: true };
};

export const updateOAuthUsername = async (userId: string, username: string, code?: string) => {
    // Validate username
    const validationResult = await validateUsername(username);
    if (!validationResult.success) {
        return { error: validationResult.message };
    }

    // Get current user
    const currentUser = await db.user.findUnique({
        where: { id: Number.parseInt(userId) },
        include: { account: true },
    });

    if (!currentUser) {
        return { error: "User not found" };
    }

    // Check if user has OAuth account (Discord or Google)
    const hasOAuthAccount = currentUser.account.some(
        (account) => account.providerId === "discord" || account.providerId === "google"
    );

    if (!hasOAuthAccount) {
        return { error: "This endpoint is only for OAuth users" };
    }

    // Check if registration codes are required
    if (!gameConfig.REGISTRATION_CODES_DISABLED && !code) {
        return { error: "Registration code is required" };
    }

    let registrationCode;
    if (!gameConfig.REGISTRATION_CODES_DISABLED && code) {
        registrationCode = await db.registration_code.findFirst({
            where: { code: code, claimerId: null },
        });

        if (!registrationCode) {
            return { error: "Invalid registration code" };
        }
    }

    // Check if username is already taken
    const existingUser = await db.user.findFirst({
        where: {
            username: username,
            NOT: { id: Number.parseInt(userId) },
        },
    });

    if (existingUser) {
        return { error: "That username is already taken" };
    }

    // Update username and complete user setup
    const assignedClass = await AuthRepository.findLeastPopulatedClass();
    await db.user.update({
        where: { id: Number.parseInt(userId) },
        data: {
            username: username,
            displayUsername: username,
            class: assignedClass,
            adminNotes: registrationCode?.note || null,
        },
    });

    // Handle registration code
    if (!gameConfig.REGISTRATION_CODES_DISABLED && registrationCode) {
        if (registrationCode.referrerId) {
            await setReferrerId(registrationCode, Number.parseInt(userId));
        } else if (!registrationCode.unlimitedUse) {
            await db.registration_code.delete({
                where: { id: registrationCode.id },
            });
        }
    }

    // Send chat announcement
    if (!gameConfig.REGISTRATION_CHATMESSAGE_DISABLED) {
        await ChatHelper.SendAnnouncementMessage("newUserRegistered", JSON.stringify({ username, id: userId }));
    }

    // Log action
    logAction({
        action: "OAUTH_USERNAME_UPDATE",
        userId: userId,
        info: {
            user: {
                username,
                email: currentUser.email,
                code: registrationCode ? registrationCode.code : null,
            },
        },
    });

    return { success: true };
};

const setReferrerId = async (registrationCode: RegistrationCodeType, userId: number) => {
    await db.registration_code.update({
        where: {
            id: registrationCode.id,
        },
        data: {
            claimerId: userId,
        },
    });
};

const addUserData = async (userId: number, registrationCode: RegistrationCodeType | undefined) => {
    const adminNotes = registrationCode?.note || null;
    const assignedClass = await AuthRepository.findLeastPopulatedClass();

    await db.user.update({
        where: {
            id: userId,
        },
        data: {
            adminNotes: adminNotes,
            class: assignedClass,
        },
    });
};

export const registerUser = async (
    headers: IncomingHttpHeaders,
    username: string,
    email: string,
    password: string,
    code: string | undefined
) => {
    if (!gameConfig.REGISTRATION_CODES_DISABLED && !code) {
        return { error: "Code is required" };
    }

    if (!email) {
        return { error: "Email is required" };
    }

    const validationResult = await validateUsername(username);
    if (!validationResult.success) {
        return { error: validationResult.message };
    }

    let registrationCode;

    if (!gameConfig.REGISTRATION_CODES_DISABLED) {
        registrationCode = await db.registration_code.findFirst({
            where: { code: code, claimerId: null },
        });

        if (!registrationCode) {
            return { error: "Invalid code" };
        }
    }

    const fetchedUser = await db.user.findFirst({
        where: {
            OR: [{ email: email }, { username: username }],
        },
    });

    if (fetchedUser) {
        if (fetchedUser.email === email) {
            return { error: "That email is already taken" };
        } else if (fetchedUser.username === username) {
            return { error: "That username is already taken" };
        }
        LogErrorStack({
            message: "Error registering user. Duplicate user found",
            error: new Error("Error registering user. Duplicate user found"),
        });
        return { error: "Error creating user - duplicate user found" };
    }

    const referrerId = registrationCode?.referrerId || null;

    const newUser = await auth.api.signUpEmail({
        headers: fromNodeHeaders(headers),
        body: {
            name: username,
            username: username,
            email: email,
            password: password,
        },
    });

    if (!newUser) {
        return { error: "Error creating user" };
    }

    await addUserData(Number.parseInt(newUser.user.id), registrationCode);

    if (!gameConfig.REGISTRATION_CODES_DISABLED && registrationCode) {
        if (referrerId) {
            await setReferrerId(registrationCode, Number.parseInt(newUser.user.id));
        } else if (!registrationCode.unlimitedUse) {
            await db.registration_code.delete({
                where: {
                    id: registrationCode.id,
                },
            });
        }
    }

    if (!gameConfig.REGISTRATION_CHATMESSAGE_DISABLED) {
        await ChatHelper.SendAnnouncementMessage(
            "newUserRegistered",
            JSON.stringify({ username, id: newUser.user.id })
        );
    }

    logAction({
        action: "REGISTER",
        userId: newUser.user.id,
        info: {
            user: {
                username,
                email: newUser.user.email,
                code: registrationCode ? registrationCode.code : null,
            },
        },
    });

    return { data: newUser.user };
};
