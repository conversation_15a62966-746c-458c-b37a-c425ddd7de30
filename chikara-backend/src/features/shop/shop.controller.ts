import gameConfig from "../../config/gameConfig.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as UserService from "../../core/user.service.js";
import * as ItemRepository from "../../repositories/item.repository.js";
import * as ShopRepository from "../../repositories/shop.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { ShopListingModel, UserModel } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";
import { ItemTypes } from "@prisma/client";
import * as UserRepository from "../../repositories/user.repository.js";

const { SHOP_ITEM_COST_MULTIPLIER } = gameConfig;

/**
 * Get all shops
 */
export const shopList = async () => {
    try {
        const shops = await ShopRepository.findAllShops();
        return { data: shops };
    } catch (error) {
        LogErrorStack({ message: "Failed to get shop list:", error });
        return { error: "Failed to retrieve shops" };
    }
};

/**
 * Get detailed information about a specific shop
 */
export const shopInfo = async (shopId: number) => {
    try {
        const shop = await ShopRepository.findShopById(shopId);

        if (!shop) {
            return { error: "Shop not found" };
        }

        return { data: shop };
    } catch (error) {
        LogErrorStack({ message: "Failed to get shop by ID:", error });
        return { error: "Failed to retrieve shop information" };
    }
};

/**
 * Process currency deduction based on shop listing currency type
 */
export const processCurrencyDeduction = (user: UserModel, shopListing: ShopListingModel, cost: number) => {
    const userUpdate: { cash?: number; gangCreds?: number; classPoints?: number } = {};

    switch (shopListing.currency) {
        case "yen": {
            if (user.cash < cost) {
                throw new Error("Not enough cash to complete purchase.");
            }
            userUpdate.cash = user.cash - cost;
            break;
        }
        case "gangCreds": {
            if (user.gangCreds < cost) {
                throw new Error("Not enough creds to complete purchase.");
            }
            userUpdate.gangCreds = user.gangCreds - cost;
            break;
        }
        case "classPoints": {
            if (user.classPoints < cost) {
                throw new Error("Not enough points to complete purchase.");
            }
            userUpdate.classPoints = user.classPoints - cost;
            break;
        }
        default: {
            throw new Error("Invalid currency type");
        }
    }

    return userUpdate;
};

/**
 * Purchase an item from a shop
 */
export const purchaseItem = async (listingId: number, amount: number, userId: number) => {
    try {
        // Validate amount
        if (!amount || !Number.isSafeInteger(amount) || amount <= 0) {
            return { error: "Invalid amount" };
        }

        // Get shop listing
        const shopListing = await ShopRepository.findShopListingById(listingId);

        if (!shopListing) {
            return { error: "Shop listing not found" };
        }

        // shopId can be null in the DB, guard against that
        if (shopListing.shopId == null) {
            return { error: "Invalid shop listing – no shop attached" };
        }

        const shop = await ShopRepository.findShopById(shopListing.shopId);

        if (!shop || shop.disabled) {
            return { error: "Shop is closed" };
        }

        // Get user
        const currentUser = await UserRepository.getUserById(userId);

        if (!currentUser) {
            return { error: "User not found" };
        }

        // Check stock and user limits
        if (shopListing.stock !== null) {
            if (shopListing.stock === 0 || shopListing.stock < amount) {
                return { error: "Not enough stock!" };
            }
            if (!currentUser.weeklyBuyLimitRemaining) {
                return { error: "Personal buy limit reached!" };
            }
            if (currentUser.weeklyBuyLimitRemaining < amount) {
                return { error: "Can't purchase this many!" };
            }
        }

        // Get item details
        if (shopListing.itemId == null) {
            return { error: "Invalid shop listing – no item attached" };
        }

        const item = await ItemRepository.findItemById(shopListing.itemId);

        if (!item) {
            return { error: "Item not found" };
        }

        // Calculate cost
        const customCost = shopListing.customCost ?? 0;
        const cashValue = item.cashValue ?? 0;

        const costPerItem = customCost > 0 ? customCost : Math.round(cashValue * gameConfig.SHOP_ITEM_COST_MULTIPLIER);
        const totalCost = costPerItem * amount;

        // Process currency deduction
        let userUpdate: { weeklyBuyLimitRemaining?: number; cash?: number; gangCreds?: number; classPoints?: number };
        try {
            userUpdate = processCurrencyDeduction(currentUser, shopListing, totalCost);
        } catch (error) {
            return { error: (error as Error).message };
        }

        // Update stock if applicable
        if (shopListing.stock !== null) {
            await ShopRepository.updateShopListingStock(shopListing.id, shopListing.stock - amount);

            // Update user's weekly buy limit
            userUpdate.weeklyBuyLimitRemaining = currentUser.weeklyBuyLimitRemaining - amount;
        }

        // Update user
        await UserService.updateUser(currentUser.id, userUpdate);

        logAction({
            action: "SHOP_PURCHASE",
            userId,
            info: {
                itemId: shopListing.itemId,
                itemName: item.name,
                quantity: amount,
            },
        });

        // Add item to user inventory
        await InventoryService.AddItemToUser({
            userId: currentUser.id,
            itemId: shopListing.itemId,
            amount,
            isTradeable: false,
        });

        return { data: {} };
    } catch (error) {
        LogErrorStack({ message: "Failed to purchase item:", error });
        return { error: "Failed to purchase item" };
    }
};

/**
 * Sell an item to a shop
 */
export const sellItem = async (userItemId: number, amount: number, userId: number) => {
    try {
        // Validate amount
        if (!amount || !Number.isSafeInteger(amount) || amount <= 0) {
            return { error: "Invalid amount" };
        }

        // Get item
        const userItem = await ItemRepository.findUserItemById(userItemId, userId);

        if (!userItem || !userItem.item) {
            return { error: "Invalid item id" };
        }

        const item = userItem.item;

        // Check if item is a quest item
        if (item.itemType === ItemTypes.quest) {
            return { error: "Can't sell quest items" };
        }

        // Get user
        const currentUser = await UserRepository.getUserById(userId);

        if (!currentUser) {
            return { error: "User not found" };
        }

        // Check if user has enough of the item
        if (!(await InventoryService.UserHasNumberOfItem(currentUser.id, item.id, amount))) {
            return { error: "You don't have enough items!" };
        }

        // Remove items from user inventory
        await InventoryService.SubtractUserItemFromUser(userItemId, amount);

        // Calculate value and update user cash
        const value = (item.cashValue ?? 0) * amount;
        await UserService.updateUser(currentUser.id, { cash: currentUser.cash + value });

        logAction({
            action: "SHOP_SELL",
            userId: userId,
            info: {
                itemId: item.id,
                itemName: item.name,
                quantity: amount,
            },
        });

        return { data: { cashValue: value } };
    } catch (error) {
        LogErrorStack({ message: "Failed to sell item:", error });
        return { error: "Failed to sell item" };
    }
};

/**
 * Get trader reputation for a user
 */
export const getTraderRep = async (userId: number, shopId: number) => {
    try {
        const filter: { userId: number; shopId?: number } = { userId };

        if (shopId) {
            filter.shopId = shopId;
        }

        const rep = await ShopRepository.getTraderReputation(filter);

        return { data: rep };
    } catch (error) {
        LogErrorStack({ message: "Failed to get trader reputation:", error });
        return { error: "Failed to get trader reputation" };
    }
};
