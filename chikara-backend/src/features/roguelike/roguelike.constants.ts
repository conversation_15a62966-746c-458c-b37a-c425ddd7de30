import gameConfig from "../../config/gameConfig.js";

const {
    MAX_NODES,
    MIN_NODES,
    DOUBLE_EDGE_CHANCE,
    BATTLE_WEIGHT,
    BUFF_WEIGHT,
    CHARACTER_WEIGHT,
    <PERSON>AVEN<PERSON>_NODE_WEIGHT,
    DEFAULT_ENCOUNTER_JAIL_DURATION_MS,
} = gameConfig;

export {
    MAX_NODES,
    MIN_NODES,
    DOUBLE_EDGE_CHANCE,
    BATTLE_WEIGHT,
    BUFF_WEIGHT,
    CHARACTER_WEIGHT,
    SCAVENGE_NODE_WEIGHT,
    DEFAULT_ENCOUNTER_JAIL_DURATION_MS,
};

export const ENCOUNTER_TYPES = {
    BASE: "base",
    BATTLE: "battle",
    BOSS: "boss",
    BUFF: "buff",
    CHARACTER: "character",
    SCAVENGE: "scavenge",
} as const;

export const CHARACTER_NAMES: string[] = [
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "???",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
] as const;

export const ENCOUNTER_LOCATIONS = [
    "church",
    "shrine",
    "mall",
    "alley",
    "school",
    "sewers",
    "themepark",
    "any",
] as const;

export type ENCOUNTER_LOCATIONS = (typeof ENCOUNTER_LOCATIONS)[number];

export const ENCOUNTER_LOCATIONS_IMAGES: string[] = [
    "mall1",
    "schoolField1",
    "housingExt2",
    "street1",
    "market",
] as const;

export const GOOD_DIALOGUE_LINES: string[] = [
    "Here's a little something to help you out. Good luck with your training!",
    "You helped me out the other day, so I wanted to give you this as a thank you. Hope it comes in handy.",
    "You're doing great at the academy. Keep it up! Here's a small token of my appreciation.",
    "You're always so kind to everyone around here. Here's a small gesture of my appreciation.",
    "I have something for you. It's not much, but I hope it'll be helpful in your studies.",
    "I've been watching you, and I think you have a lot of potential. Here, take this!",
    "I wanted to give you something for all the times you've helped me out. Thank you!",
    "I can see you're working hard to improve. Keep it up! Here's something to motivate you.",
    "I thought this might come in handy for someone like you. Take it!",
] as const;

export const BAD_DIALOGUE_LINES: string[] = [
    "Looks like someone was carrying too much cash on them.",
    "Don't worry, I won't be hurting you. I just need your money.",
    "You should learn to be more careful with your possessions.",
    "I'm taking your money and there's nothing you can do about it.",
    "Hey there, give me all your money or else!",
] as const;

export const APOLLO_DIALOGUE: string[] = [
    "Woof!",
    "Bark bark bark!",
    "Did you know that Godzilla has survived entering a black hole?",
] as const;

export const ROGUELIKE_BATTLE_TYPES = { NORMAL: "normal", BOSS: "boss" } as const;

// constrain the width of the map since the frontend can't handle wider trees well
export const MAX_DOUBLE_EDGED_NODES = 4 as const;
