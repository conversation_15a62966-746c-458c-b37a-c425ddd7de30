import gameConfig from "../../config/gameConfig.js";
import * as <PERSON><PERSON>y<PERSON><PERSON><PERSON> from "./bounty.helpers.js";
import * as BountyRepository from "../../repositories/bounty.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { BountyModel } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";
import { emitBountyPlaced } from "../../core/events/index.js";
import * as UserRepository from "../../repositories/user.repository.js";

interface BountyListResponse {
    data?: BountyModel[];
    error?: string;
    statusCode?: number;
}

interface PlaceBountyParams {
    userId: number;
    bountyAmount: number;
    targetId: number;
    reason: string;
}

interface PlaceBountyResponse {
    data?: BountyModel;
    error?: string;
    statusCode?: number;
}

interface DeleteBountyParams {
    id: number;
}

interface DeleteBountyResponse {
    data?: { success: boolean };
    error?: string;
    statusCode?: number;
}

export const bountyList = async (): Promise<BountyListResponse> => {
    try {
        const bounties = await BountyRepository.findAllBounties();
        return { data: bounties };
    } catch (error: unknown) {
        LogErrorStack({ error });
        return { error: "something went wrong", statusCode: 400 };
    }
};

export const activeBountyList = async (): Promise<BountyListResponse> => {
    try {
        const bounties = await BountyRepository.findActiveBountiesWithUsers();
        return { data: bounties };
    } catch (error: unknown) {
        LogErrorStack({ error });
        return { error: "something went wrong", statusCode: 400 };
    }
};

export const placeBounty = async ({
    userId,
    bountyAmount,
    targetId,
    reason,
}: PlaceBountyParams): Promise<PlaceBountyResponse> => {
    try {
        if (!bountyAmount || !targetId) {
            return { error: "Missing amount or target", statusCode: 400 };
        }

        if (bountyAmount < gameConfig.MIN_BOUNTY) {
            return { error: "Amount too low", statusCode: 400 };
        }

        const targetInfo = await BountyRepository.getUserTypeAndLevel(targetId);
        if (!targetInfo) {
            return { error: "Invalid target", statusCode: 400 };
        }
        if (targetInfo.userType === "admin") {
            return { error: "Can't place bounties on staff", statusCode: 400 };
        }
        if (targetInfo.level < gameConfig.BOUNTY_MIN_LEVEL) {
            return { error: "Targets level is too low", statusCode: 400 };
        }

        const currentUser = await UserRepository.getUserById(userId);
        if (!currentUser) {
            return { error: "Invalid user", statusCode: 400 };
        }

        const bountyCost: number = bountyAmount * (1 + gameConfig.BOUNTY_FEE);
        if (currentUser.cash < bountyCost) {
            return { error: "Not enough cash", statusCode: 400 };
        }

        const updatedUser = await UserRepository.decrementUserCash(userId, bountyCost);
        if (!updatedUser) {
            return { error: "Failed to update user cash", statusCode: 400 };
        }

        const bounty = await BountyHelper.ApplyBounty(targetId, bountyAmount, reason, userId);

        await emitBountyPlaced({
            userId,
            targetId,
            amount: bountyAmount,
        });

        logAction({
            action: "BOUNTY_PLACED",
            userId: userId,
            info: {
                amount: bountyAmount,
                reason: reason,
                targetId: targetId,
            },
        });

        return { data: bounty };
    } catch (error: unknown) {
        LogErrorStack({ error });
        return { error: "Something went wrong", statusCode: 400 };
    }
};

export const deleteBounty = async ({ id }: DeleteBountyParams): Promise<DeleteBountyResponse> => {
    try {
        await BountyRepository.deleteBountyById(id);
        return { data: { success: true } };
    } catch (error: unknown) {
        LogErrorStack({ error });
        return { error: "something went wrong", statusCode: 400 };
    }
};

export default {
    bountyList,
    activeBountyList,
    placeBounty,
    deleteBounty,
};
