import gameConfig from "../../config/gameConfig.js";
import * as NotificationService from "../../core/notification.service.js";
import * as profileCommentRepository from "../../repositories/profilecomment.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { NotificationTypes } from "../../types/notification.js";
import { LogErrorStack } from "../../utils/log.js";

export const getComments = async (userId: number) => {
    try {
        const comments = await profileCommentRepository.findManyComments(userId);
        return { data: comments };
    } catch (error) {
        LogErrorStack({ message: "Failed to fetch profile comments:", error });
        return { error: "Failed to fetch profile comments", statusCode: 500 };
    }
};

export const CommentOnProfile = async (
    senderId: number,
    receiverId: number,
    message: string,
    chatBannedUntil: bigint | null
) => {
    try {
        if (chatBannedUntil != null) {
            return { error: "Cannot profile comment while chat banned", statusCode: 400 };
        }

        if (senderId == receiverId) {
            return { error: "Can't comment on your own profile", statusCode: 400 };
        }

        if (message.length > gameConfig.PROFILE_COMMENT_MAX_LENGTH) {
            return { error: "Comment too long!", statusCode: 400 };
        }

        await profileCommentRepository.createComment(senderId, receiverId, message);

        logAction({
            action: "PROFILE_COMMENT_LEFT",
            userId: senderId,
            info: {
                receiverId: receiverId,
                message: message,
            },
        });

        await NotificationService.NotifyUser(receiverId, NotificationTypes.profile_comment, {
            senderId: senderId,
            type: "profile",
        });

        return { data: "Profile comment created successfully" };
    } catch (error) {
        LogErrorStack({ message: "Failed to send profile comment:", error });
        return { error: "Failed to create profile comment", statusCode: 400 };
    }
};
