import gameConfig from "../../config/gameConfig.js";
import {
    findUserPetByIdAndOwner,
    updatePetEvolution,
    updatePetEvolutionProgress,
    updatePetLevelAndXp,
    updatePetXp,
} from "../../repositories/pets.repository.js";
import { Evolution, EvolutionStage } from "./pets.types.js";
import { PetModel, UserPetModel } from "../../lib/db.js";
import { getNow } from "../../utils/dateHelpers.js";

const { EGG_TIME_PER_PROGRESS_POINT } = gameConfig;

export const updateEggProgress = async (userPets: UserPetModel[]) => {
    if (!userPets) return userPets;
    const eggs = userPets.filter((pet) => pet.evolution.current === "egg");
    if (!eggs) return userPets;

    const currentTime = getNow();

    for (const egg of eggs) {
        if (!egg.evolution.requiredEggProgress || egg.evolution.progress >= egg.evolution.requiredEggProgress) {
            continue;
        }

        const eggStartDateTime = egg.createdAt;
        const timeDiff = currentTime.getTime() - eggStartDateTime.getTime();
        const progressPoints = Math.floor(timeDiff / gameConfig.EGG_TIME_PER_PROGRESS_POINT);
        if (progressPoints > egg.evolution.progress) {
            const updatedProgress = Math.min(
                egg.evolution.progress + progressPoints,
                egg.evolution.requiredEggProgress
            );
            await updatePetEvolutionProgress(egg.id, {
                ...egg.evolution,
                progress: updatedProgress,
            });
            egg.evolution.progress = updatedProgress;
        }
    }
    return userPets;
};

/**
 * Verifies that a pet belongs to a user
 * @param userId The ID of the user
 * @param userPetId The ID of the user's pet
 * @returns The pet object if verified
 * @throws {Error} If ownership verification fails
 */
export async function verifyPetOwnership(userId: number, userPetId: number) {
    const userPet = await findUserPetByIdAndOwner(userId, userPetId);

    if (!userPet) {
        throw new Error("Pet not found or does not belong to this user");
    }

    return userPet;
}

/**
 * Gets the current evolution stage data for a pet
 * @param pet The pet model
 * @param evolutionStage The name of the evolution stage to retrieve
 * @returns The evolution stage data or null if not found
 */
export function getPetEvolutionStage(pet: PetModel, evolutionStage: string): EvolutionStage | null {
    if (!pet || !pet.evolution_stages) {
        return null;
    }

    const stages = pet.evolution_stages as EvolutionStage[];
    return stages.find((stage) => stage.stage === evolutionStage) || null;
}

export function calculateEnergyGain(foodType: string, quantity: number) {
    // Base energy gain is 10 per food item
    let baseGain = quantity * 10;

    // Apply food type modifiers
    switch (foodType) {
        case "premium": {
            baseGain *= 1.5;
            break;
        }
        case "basic": {
            baseGain *= 0.8;
            break;
        }
    }

    return Math.round(baseGain);
}

export function calculateHappinessGain(actionType: string, duration: number) {
    let baseGain = 0;

    switch (actionType) {
        case "play": {
            baseGain = duration * 0.5; // 0.5 happiness per second
            break;
        }
        case "feed": {
            baseGain = duration * 5; // 5 happiness per food item
            break;
        }
        case "train": {
            baseGain = duration * 0.2; // 0.2 happiness per second
            break;
        }
    }

    return Math.round(baseGain);
}

export function calculateXpGain(actionType: string, duration: number) {
    let baseGain = 0;

    switch (actionType) {
        case "play": {
            baseGain = duration * 0.2; // 0.2 XP per second
            break;
        }
        case "train": {
            baseGain = duration * 0.5; // 0.5 XP per second
            break;
        }
    }

    return Math.round(baseGain);
}

/**
 * Calculates and returns the updated evolution progress based on pet's level and evolution data
 * @param currentLevel - Pet's current level
 * @param evolution - Pet's current evolution data
 * @returns Updated evolution data
 */
export function calculateEvolutionProgress(currentLevel: number, evolution: Evolution) {
    // Convert to the proper type
    const evolutionData = evolution as {
        current: string;
        next: string;
        progress: number;
        requiredLevel: number;
    };

    // If there's no next evolution stage, return current evolution
    if (!evolutionData.next) {
        return evolutionData;
    }

    // Calculate progress percentage based on level compared to required level
    const progress = Math.min(Math.floor((currentLevel / evolutionData.requiredLevel) * 100), 100);

    // Return updated evolution object
    return {
        ...evolutionData,
        progress,
    };
}

/**
 * Checks if a pet is ready to evolve based on its level and evolution data
 * @param userPet - The user's pet to check for evolution
 * @returns Boolean indicating if pet can evolve
 */
export function canPetEvolve(userPet: UserPetModel) {
    const evolution = userPet.evolution;

    if (!userPet.evolution || evolution.next === "") return false;

    if (evolution.current === "egg") {
        if (!evolution.requiredEggProgress || evolution.progress < evolution.requiredEggProgress) {
            return false;
        }
    } else if (!evolution.requiredLevel || userPet.level < evolution.requiredLevel || userPet.happiness < 100) {
        return false;
    }

    return true;
}

/**
 * Adds XP to a pet and handles level up
 * @param userPet - The user's pet to update
 * @param xpGain - The amount of XP to add
 * @returns Updated user pet object
 */
export async function addPetXp(userPet: UserPetModel<{ include: { pet: true } }>, xpGain: number) {
    const newPetXp = userPet.xp + xpGain;

    // Pet level up
    if (newPetXp >= userPet.nextLevelXp && userPet.level < userPet.pet.maxLevel) {
        const newLevel = userPet.level + 1;
        const newNextLevelXp = userPet.nextLevelXp * 1.5; // Increase XP needed for next level by 50%

        return await updatePetLevelAndXp(userPet.id, newLevel, 0, Math.floor(newNextLevelXp));
    }

    return await updatePetXp(userPet.id, newPetXp, userPet.nextLevelXp);
}
