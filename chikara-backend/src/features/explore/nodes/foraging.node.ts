import type { ExploreNodeLocation } from "@prisma/client";
import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import * as ShrineHelper from "../../shrine/shrine.helpers.js";
import { logAction } from "../../../lib/actionLogger.js";
import type { ItemModel, StatusEffectModel, UserModel } from "../../../lib/db.js";
import { db } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
import { emitResourceGathered } from "../../../core/events/game-event.service.js";

/**
 * Foraging node configuration
 */
export const FORAGING_CONFIG = {
    FAIL_CHANCE: 0.2, // 20% chance of bad outcome (lower than mining)
    INJURY_CHANCE: 0.5, // 50% chance to get an injury on bad outcome / 50% chance to lose energy
    SUCCESS_BONUS_CHANCE: 0.25, // 25% chance to get bonus plants on success (higher than mining)
    DEFAULT_ENERGY_COST: 2, // Energy cost for foraging attempt (lower than mining)
    FORAGING_TIMEOUT_MS: 90 * 1000, // 90 seconds to complete foraging (faster than mining)
    FORAGING_TYPES: ["herbs", "berries", "mushrooms", "flowers", "roots"] as const,
    INJURY_TYPES: ["thorn_scratch", "poisonous_plant", "exhaustion"] as const,
} as const;

/**
 * Interface for foraging result data
 */
export interface ForagingResult {
    success: boolean;
    foragingType: string;
    itemReward?: Omit<ItemModel, "createdAt" | "updatedAt">;
    itemQuantity?: number;
    bonusReward?: Omit<ItemModel, "createdAt" | "updatedAt">;
    bonusQuantity?: number;
    injury?: StatusEffectModel;
    experienceGained?: number;
    message?: string;
}

/**
 * Interface for foraging operation data
 */
export interface ForagingOperation {
    foragingType: string;
    foragingValidUntil: number;
    difficulty: "easy" | "medium" | "hard";
    energyCost: number;
}

/**
 * Get potential plant drops for foraging in explore mode
 */
export const findExploreForagingDrops = async (
    userLevel: number,
    location: ExploreNodeLocation,
    foragingType: string
) => {
    const mappedLocation = mapExploreLocationToLocationTypes(location);

    return await db.drop_chance.findMany({
        where: {
            dropChanceType: "scavenge", // Use scavenge type for foraging drops until foraging type is added
            scavengeType: foragingType, // Reusing scavengeType field for foraging type
            location: {
                in: [mappedLocation, "any"],
            },
            minLevel: {
                lte: userLevel,
            },
            maxLevel: {
                gte: userLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: true,
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};

/**
 * Handle successful foraging outcome - gives plants/herbs
 */
export const handleForagingSuccess = async (
    user: UserModel,
    location: ExploreNodeLocation,
    foragingType: string,
    difficulty: "easy" | "medium" | "hard"
) => {
    const potentialDrops = await findExploreForagingDrops(user.level, location, foragingType);

    if (!potentialDrops || potentialDrops.length === 0) {
        return null;
    }

    // Pick a random item from the potential drops
    const randomIndex: number = Math.floor(Math.random() * potentialDrops.length);
    const selectedItem = potentialDrops[randomIndex];

    // Check if itemId and item are not null
    if (selectedItem.itemId === null || !selectedItem.item) {
        return null;
    }

    // Calculate quantity based on difficulty
    const difficultyMultiplier = difficulty === "easy" ? 1 : difficulty === "medium" ? 1.3 : 1.5;
    let baseQuantity = selectedItem.quantity;
    
    // Apply shrine gatheringAmount buff if active
    const gatheringAmountBuff = await ShrineHelper.dailyBuffIsActive("gatheringAmount", user.id);
    if (gatheringAmountBuff) {
        baseQuantity = Math.round(baseQuantity * gatheringAmountBuff);
    }
    
    const finalQuantity = Math.max(1, Math.floor(baseQuantity * difficultyMultiplier));

    // Add the item to the user's inventory
    await InventoryService.AddItemToUser({
        userId: user.id,
        itemId: selectedItem.itemId,
        amount: finalQuantity,
        isTradeable: true,
    });

    // Emit resource gathering event for quest objectives
    await emitResourceGathered({
        userId: user.id,
        itemId: selectedItem.itemId,
        quantity: finalQuantity,
        activityType: "foraging",
        location: mapExploreLocationToLocationTypes(location),
    });

    // Calculate experience gained based on difficulty and item rarity
    const baseExp = 10; // Lower than mining
    const diffExp = difficulty === "easy" ? 1 : difficulty === "medium" ? 1.3 : 1.5;
    const experienceGained = Math.floor(baseExp * diffExp);

    // Check for bonus reward
    let bonusResult = null;
    if (Math.random() <= FORAGING_CONFIG.SUCCESS_BONUS_CHANCE) {
        // 25% chance for bonus plants
        const bonusItem = potentialDrops[Math.floor(Math.random() * potentialDrops.length)];
        if (bonusItem.itemId && bonusItem.item) {
            let bonusBaseQuantity = bonusItem.quantity;
            
            // Apply shrine gatheringAmount buff to bonus item if active
            if (gatheringAmountBuff) {
                bonusBaseQuantity = Math.round(bonusBaseQuantity * gatheringAmountBuff);
            }
            
            const bonusQuantity = Math.max(1, Math.floor(bonusBaseQuantity * 0.6));
            await InventoryService.AddItemToUser({
                userId: user.id,
                itemId: bonusItem.itemId,
                amount: bonusQuantity,
                isTradeable: true,
            });

            // Emit resource gathering event for bonus item
            await emitResourceGathered({
                userId: user.id,
                itemId: bonusItem.itemId,
                quantity: bonusQuantity,
                activityType: "foraging",
                location: mapExploreLocationToLocationTypes(location),
            });

            bonusResult = {
                bonusReward: bonusItem.item,
                bonusQuantity,
            };
        }
    }

    logAction({
        action: "EXPLORE_FORAGING_SUCCESS",
        userId: user.id,
        info: {
            itemId: selectedItem.item.id,
            itemName: selectedItem.item.name,
            quantity: finalQuantity,
            location: location,
            foragingType: foragingType,
            difficulty: difficulty,
            experienceGained,
            bonusReward: bonusResult?.bonusReward?.name || null,
        },
    });

    return {
        itemReward: selectedItem.item,
        itemQuantity: finalQuantity,
        experienceGained,
        ...bonusResult,
    };
};

/**
 * Handle failed foraging outcome - applies injury
 */
export const handleForagingFailure = async (
    currentUser: UserModel,
    location: ExploreNodeLocation,
    foragingType: string,
    difficulty: "easy" | "medium" | "hard"
) => {
    const outcome: { injury?: StatusEffectModel } = {};

    // Apply foraging-related injury
    const injuryTypes = ["Physical", "Mental"] as const;
    const injuryType = injuryTypes[Math.floor(Math.random() * injuryTypes.length)];

    const injury = await StatusEffectService.GetRandomInjury("Minor", injuryType);
    if (injury) {
        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);
        outcome.injury = injury;

        NotificationService.NotifyUser(
            currentUser.id,
            NotificationTypes.injured,
            {
                reason: "foraging accident",
                injury: injury.name,
                injuryTier: injury.tier,
            },
            true
        );

        logAction({
            action: "EXPLORE_FORAGING_INJURY",
            userId: currentUser.id,
            info: {
                injury: injury.name,
                injuryTier: injury.tier,
                location: location,
                foragingType: foragingType,
                difficulty: difficulty,
            },
        });
    }

    return outcome;
};

/**
 * Determine foraging difficulty based on location and user level
 */
export const determineForagingDifficulty = (
    location: ExploreNodeLocation,
    userLevel: number
): "easy" | "medium" | "hard" => {
    // Base difficulty on location characteristics
    const locationDifficulty = {
        shibuya: "medium", // Urban environment makes foraging harder
        shinjuku: "hard", // Dense business district
        bunkyo: "easy", // Educational district with parks
        chiyoda: "medium", // Government area with gardens
        minato: "easy", // Port area with natural spaces
    } as const;

    const baseDifficulty = locationDifficulty[location] || "easy";

    // Adjust based on user level (foraging is generally easier than mining)
    if (userLevel >= 25) {
        return baseDifficulty === "easy" ? "easy" : baseDifficulty === "medium" ? "medium" : "hard";
    } else if (userLevel >= 10) {
        return baseDifficulty === "easy" ? "easy" : baseDifficulty === "medium" ? "easy" : "medium";
    }
    return "easy";
};

/**
 * Select random foraging type based on location
 */
export const selectForagingType = (location: ExploreNodeLocation): string => {
    const locationForagingTypes = {
        shibuya: ["herbs", "flowers"], // Urban plants
        shinjuku: ["herbs", "roots"], // Hardy plants in business district
        bunkyo: ["berries", "flowers", "herbs"], // Educational district with diverse plants
        chiyoda: ["flowers", "herbs"], // Government gardens
        minato: ["berries", "mushrooms"], // Port area with natural growth
    } as const;

    const availableTypes = locationForagingTypes[location] || ["herbs"];
    return availableTypes[Math.floor(Math.random() * availableTypes.length)];
};

/**
 * Initialize foraging operation
 */
export const initializeForaging = async (userId: number, location: ExploreNodeLocation) => {
    const user = await UserRepository.getUserById(userId);
    if (!user) {
        throw new Error("User not found");
    }

    const foragingType = selectForagingType(location);
    const difficulty = determineForagingDifficulty(location, user.level);
    const energyCost =
        FORAGING_CONFIG.DEFAULT_ENERGY_COST * (difficulty === "easy" ? 1 : difficulty === "medium" ? 1.2 : 1.5);
    const foragingValidUntil = Date.now() + FORAGING_CONFIG.FORAGING_TIMEOUT_MS;

    logAction({
        action: "EXPLORE_FORAGING_INITIATED",
        userId: userId,
        info: {
            location: location,
            foragingType: foragingType,
            difficulty: difficulty,
            energyCost: energyCost,
        },
    });

    return {
        foragingType,
        difficulty,
        energyCost,
        foragingValidUntil,
    };
};

/**
 * Process foraging operation
 */
export const processForaging = async (
    userId: number,
    nodeId: number,
    location: ExploreNodeLocation,
    foragingType: string,
    difficulty: "easy" | "medium" | "hard"
) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            success: false,
            message: "User not found",
        };
    }

    const goodOutcome: boolean = Math.random() > FORAGING_CONFIG.FAIL_CHANCE;
    const result: ForagingResult = {
        success: goodOutcome,
        foragingType,
    };

    if (goodOutcome) {
        // Success - get plant/herb reward
        const foragingResult = await handleForagingSuccess(currentUser, location, foragingType, difficulty);
        if (foragingResult) {
            result.itemReward = foragingResult.itemReward;
            result.itemQuantity = foragingResult.itemQuantity;
            result.experienceGained = foragingResult.experienceGained;
            result.bonusReward = foragingResult.bonusReward;
            result.bonusQuantity = foragingResult.bonusQuantity;

            if (result.bonusReward) {
                result.message = `You successfully foraged ${result.itemQuantity} ${result.itemReward?.name} and found a bonus of ${result.bonusQuantity} ${result.bonusReward.name}!`;
            } else {
                result.message = `You successfully foraged ${result.itemQuantity} ${result.itemReward?.name}!`;
            }
        } else {
            result.message = "You searched the area thoroughly but didn't find any valuable plants.";
        }
    } else {
        // Failure - get injury
        const failureResult = await handleForagingFailure(currentUser, location, foragingType, difficulty);
        result.injury = failureResult.injury;

        if (failureResult.injury) {
            result.message = "A foraging accident occurred! You've been injured in the process.";
        } else {
            result.message = "The foraging attempt was unsuccessful.";
        }
    }

    return {
        success: true,
        message: "Foraging operation completed",
        data: result,
    };
};

/**
 * Main handler for foraging encounters in explore nodes
 * This handles the initial interaction with a foraging node
 */
export const handleForagingEncounter = async (userId: number, nodeId: number, location: ExploreNodeLocation) => {
    try {
        // Initialize the foraging operation
        const foragingOperation = await initializeForaging(userId, location);

        // Update the node metadata with the foraging operation details
        const metadataUpdated = await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
            foragingType: foragingOperation.foragingType,
            difficulty: foragingOperation.difficulty,
            energyCost: foragingOperation.energyCost,
            foragingValidUntil: foragingOperation.foragingValidUntil,
        });

        if (!metadataUpdated) {
            return {
                success: false,
                message: "Failed to initialize foraging operation",
            };
        }

        // Estimate reward based on difficulty
        const estimatedReward =
            foragingOperation.difficulty === "easy"
                ? "low"
                : foragingOperation.difficulty === "medium"
                  ? "medium"
                  : "high";

        return {
            success: true,
            message: "Foraging area prepared. Click 'Gather Plants' to begin collection.",
            data: {
                action: "foraging_ready",
                nodeId,
                foragingType: foragingOperation.foragingType,
                difficulty: foragingOperation.difficulty,
                energyCost: foragingOperation.energyCost,
                estimatedReward,
            },
        };
    } catch (error) {
        logAction({
            action: "EXPLORE_FORAGING_ERROR",
            userId: userId,
            info: {
                nodeId,
                location,
                error: error instanceof Error ? error.message : "Unknown error",
            },
        });

        return {
            success: false,
            message: "Failed to prepare foraging area",
        };
    }
};
