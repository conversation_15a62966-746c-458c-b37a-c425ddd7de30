import type { ExploreNodeLocation } from "@prisma/client";
import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserService from "../../../core/user.service.js";
import * as UniqueItemHelpers from "../../item/uniqueitem.helpers.js";
import * as ShrineHelper from "../../shrine/shrine.helpers.js";
import * as TalentHelper from "../../talents/talents.helpers.js";
import { logAction } from "../../../lib/actionLogger.js";
import type { ItemModel, UserModel } from "../../../lib/db.js";
import { db } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import { emitEncounterCompleted, emitItemDropped } from "../../../core/events/index.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import * as ItemRepository from "../../../repositories/item.repository.js";

/**
 * Character encounter constants
 */
export const CHARACTER_NAMES: string[] = [
    "Daiki",
    "Hana",
    "Hiroshi",
    "Izumi",
    "Kenzo",
    "Mai",
    "Tashiro",
    "Yuta",
    "???",
    "Kazuya",
    "Katsuro",
] as const;

export const ENCOUNTER_LOCATIONS_IMAGES: string[] = [
    "mall1",
    "schoolField1",
    "housingExt2",
    "street1",
    "market",
] as const;

export const GOOD_DIALOGUE_LINES: string[] = [
    "Here's a little something to help you out. Good luck with your training!",
    "You helped me out the other day, so I wanted to give you this as a thank you. Hope it comes in handy.",
    "You're doing great at the academy. Keep it up! Here's a small token of my appreciation.",
    "You're always so kind to everyone around here. Here's a small gesture of my appreciation.",
    "I have something for you. It's not much, but I hope it'll be helpful in your studies.",
    "I've been watching you, and I think you have a lot of potential. Here, take this!",
    "I wanted to give you something for all the times you've helped me out. Thank you!",
    "I can see you're working hard to improve. Keep it up! Here's something to motivate you.",
    "I thought this might come in handy for someone like you. Take it!",
] as const;

export const BAD_DIALOGUE_LINES: string[] = [
    "Looks like someone was carrying too much cash on them.",
    "Don't worry, I won't be hurting you. I just need your money.",
    "You should learn to be more careful with your possessions.",
    "I'm taking your money and there's nothing you can do about it.",
    "Hey there, give me all your money or else!",
] as const;

export const APOLLO_DIALOGUE: string[] = [
    "Woof!",
    "Bark bark bark!",
    "Did you know that Godzilla has survived entering a black hole?",
] as const;

/**
 * Configuration constants
 */
export const CHARACTER_ENCOUNTER_CONFIG = {
    DEFAULT_ENCOUNTER_JAIL_DURATION_MS: 10 * 60 * 1000, // 10 minutes
    APOLLO_ENCOUNTER_CHANCE: 0.05, // 5% chance
    ITEM_DROP_CHANCE: 0.4, // 40% chance
    HOSPITALIZATION_CHANCE: 0.05, // 5% chance if mugged
    JAIL_CHANCE: 0.15, // 15% chance if mugged and not hospitalized
    APOLLO_HEAL_PERCENTAGE: 0.3, // 30% heal
} as const;

/**
 * Character dialogue interface for explore feature
 */
export interface ExploreCharacterDialogue {
    character: string;
    location: string;
    line: string;
    isItemDrop: boolean;
    rewards: number | ItemModel;
    mugged: boolean;
    crateReward?: ItemModel;
    hospitalised?: boolean;
    jailed?: boolean;
    injury?: string;
    injuryTier?: string;
}

/**
 * Utility function to generate random integer between min and max (inclusive)
 */
function randomIntFromInterval(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1) + min);
}

/**
 * Generate character dialogue based on outcome and user level
 */
export const generateCharacterDialogue = (goodOutcome: boolean, userLevel: number): ExploreCharacterDialogue => {
    const selectedCharacter: string = CHARACTER_NAMES[Math.floor(Math.random() * CHARACTER_NAMES.length)];
    const selectedLocation: string =
        ENCOUNTER_LOCATIONS_IMAGES[Math.floor(Math.random() * ENCOUNTER_LOCATIONS_IMAGES.length)];
    const selectedLine: string = goodOutcome
        ? GOOD_DIALOGUE_LINES[Math.floor(Math.random() * GOOD_DIALOGUE_LINES.length)]
        : BAD_DIALOGUE_LINES[Math.floor(Math.random() * BAD_DIALOGUE_LINES.length)];
    const cashReward: number = randomIntFromInterval(20 + userLevel * 10, 20 + userLevel * 30);

    // 5% chance of encountering Apollo
    if (goodOutcome && Math.random() <= CHARACTER_ENCOUNTER_CONFIG.APOLLO_ENCOUNTER_CHANCE) {
        const apolloDialogue: string = APOLLO_DIALOGUE[Math.floor(Math.random() * APOLLO_DIALOGUE.length)];
        return {
            character: "Apollo",
            location: selectedLocation,
            line: apolloDialogue,
            isItemDrop: false,
            rewards: 0,
            mugged: false,
        };
    }

    // 40% chance of item drop for good outcomes
    if (goodOutcome && Math.random() <= CHARACTER_ENCOUNTER_CONFIG.ITEM_DROP_CHANCE) {
        return {
            character: selectedCharacter,
            location: selectedLocation,
            line: selectedLine,
            isItemDrop: true,
            rewards: cashReward,
            mugged: !goodOutcome,
        };
    }

    return {
        character: selectedCharacter,
        location: selectedLocation,
        line: selectedLine,
        isItemDrop: false,
        rewards: cashReward,
        mugged: !goodOutcome,
    };
};

/**
 * Get item drop for explore character encounters
 */
export const getExploreDropId = async (user: UserModel, location: ExploreNodeLocation) => {
    const locationType = mapExploreLocationToLocationTypes(location);

    // Query for potential drops for explore encounters
    // TODO - Add explore-specific drops, currently just using mapped roguelike drops
    const potentialDrops = await db.drop_chance.findMany({
        where: {
            dropChanceType: "roguelike",
            location: {
                in: [locationType, "any"],
            },
            dropRate: {
                gt: 0,
            },
        },
        orderBy: {
            dropRate: "asc",
        },
    });

    // Check for shrine buff for rare drops
    const shrineBuffActive = await ShrineHelper.dailyBuffIsActive("rareDrops", user.id);

    // Roll for each potential drop
    for (const drop of potentialDrops) {
        let dropRate = drop.dropRate;

        // Apply shrine buff to rare drops
        if (shrineBuffActive && dropRate < 0.08) {
            dropRate *= shrineBuffActive;
        }

        // Apply explore-specific drop rate modifier (less generous than roguelike)
        dropRate = dropRate / 6.0; // More conservative than roguelike's 4.5

        // Further reduce very low drop rates
        if (dropRate < 0.05) {
            dropRate = dropRate * 0.3;
        }

        if (Math.random() <= dropRate) {
            return drop.itemId || null;
        }
    }

    return null; // No drop
};

/**
 * Apply encounter rewards to the user
 */
export const applyEncounterReward = async (
    dialogue: ExploreCharacterDialogue,
    currentUser: UserModel,
    location: ExploreNodeLocation
) => {
    if (dialogue.isItemDrop) {
        const droppedItemId = await getExploreDropId(currentUser, location);

        // Handle main item drop
        if (droppedItemId && droppedItemId !== 0) {
            const droppedItem = await ItemRepository.findItemById(droppedItemId);
            if (droppedItem) {
                await InventoryService.AddItemToUser({
                    userId: currentUser.id,
                    itemId: droppedItem.id,
                    amount: 1,
                    isTradeable: true,
                });
                dialogue.rewards = droppedItem;

                // Emit item dropped event
                await emitItemDropped({
                    userId: currentUser.id,
                    itemId: droppedItem.id,
                    quantity: 1,
                    source: "encounter",
                    location: mapExploreLocationToLocationTypes(location),
                });

                logAction({
                    action: "EXPLORE_CHARACTER_ENCOUNTER_ITEM_DROP",
                    userId: currentUser.id,
                    info: {
                        itemId: droppedItem.id,
                        itemName: droppedItem.name,
                        location: location,
                    },
                });

                return dialogue;
            }
        }
    }

    // If no item drop occurred, convert to cash reward
    dialogue.isItemDrop = false;

    // Apply wealth item multiplier if equipped
    if (
        !dialogue.mugged &&
        typeof dialogue.rewards === "number" &&
        dialogue.rewards > 0 &&
        (await UniqueItemHelpers.IsWealthItemEquipped(currentUser))
    ) {
        dialogue.rewards = Math.round(dialogue.rewards * 1.25);
    }

    // Apply shrine yenEarnings buff if active
    if (!dialogue.mugged && typeof dialogue.rewards === "number" && dialogue.rewards > 0) {
        const yenEarningsBuff = await ShrineHelper.dailyBuffIsActive("yenEarnings", currentUser.id);
        if (yenEarningsBuff) {
            dialogue.rewards = Math.round(dialogue.rewards * yenEarningsBuff);
        }
    }

    // Apply cash reward
    if (typeof dialogue.rewards === "number" && dialogue.rewards > 0) {
        const updatedCash = currentUser.cash + dialogue.rewards;
        await UserService.updateUser(currentUser.id, { cash: updatedCash });

        logAction({
            action: "EXPLORE_CHARACTER_ENCOUNTER_CASH_REWARD",
            userId: currentUser.id,
            info: {
                rewards: dialogue.rewards,
                location: location,
            },
        });
    }

    return dialogue;
};

/**
 * Get mug chance based on user level (similar to roguelike highscore logic)
 */
const getMugChance = (userLevel: number): number => {
    return userLevel < 4 ? 0 : 0.2; // 20% chance if level 4 or higher
};

/**
 * Handle character encounter interaction for explore nodes
 */
export const handleCharacterEncounter = async (userId: number, nodeId: number, location: ExploreNodeLocation) => {
    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            success: false,
            message: "User not found",
        };
    }

    emitEncounterCompleted({ userId, encounterId: nodeId, location });

    const mugChance = getMugChance(currentUser.level);
    const goodOutcome = Math.random() > mugChance;

    let dialogue = generateCharacterDialogue(goodOutcome, currentUser.level);

    const updateValues: {
        currentHealth?: number;
        cash?: number;
        jailedUntil?: bigint;
        jailReason?: string;
    } = {};

    // Handle Apollo special case
    if (dialogue.character === "Apollo") {
        const heal = Math.round(currentUser.health * CHARACTER_ENCOUNTER_CONFIG.APOLLO_HEAL_PERCENTAGE);
        updateValues.currentHealth = Math.min(currentUser.currentHealth + heal, currentUser.health);

        logAction({
            action: "EXPLORE_CHARACTER_ENCOUNTER_HEALED",
            userId: currentUser.id,
            info: {
                heal: heal,
                location: location,
            },
        });

        // Apply user updates
        if (Object.keys(updateValues).length > 0) {
            await UserService.updateUser(currentUser.id, updateValues);
        }

        return {
            success: true,
            message: "You encountered Apollo and were healed!",
            data: {
                action: "character_encounter",
                dialogue: dialogue,
                healed: true,
            },
        };
    }

    // Handle rewards or mugging
    if (dialogue.rewards) {
        if (goodOutcome) {
            dialogue = await applyEncounterReward(dialogue, currentUser, location);
        } else if (typeof dialogue.rewards === "number") {
            // Handle mugging
            const mugAmount = Math.min(dialogue.rewards, currentUser.cash);
            dialogue.rewards = mugAmount;
            updateValues.cash = Math.max(0, currentUser.cash - mugAmount);

            logAction({
                action: "EXPLORE_CHARACTER_ENCOUNTER_MUGGED",
                userId: currentUser.id,
                info: {
                    rewards: mugAmount,
                    location: location,
                },
            });
        }
    }

    // Handle negative consequences for bad outcomes
    if (!goodOutcome) {
        const escapeArtistTalent = await TalentHelper.UserHasEscapeArtistTalent(currentUser.id);
        const jailChance = escapeArtistTalent
            ? CHARACTER_ENCOUNTER_CONFIG.JAIL_CHANCE * (escapeArtistTalent.modifier ?? 1)
            : CHARACTER_ENCOUNTER_CONFIG.JAIL_CHANCE;

        if (Math.random() <= CHARACTER_ENCOUNTER_CONFIG.HOSPITALIZATION_CHANCE) {
            // 5% chance to be hospitalized
            dialogue.hospitalised = true;

            const injury = await StatusEffectService.GetRandomInjury("Minor");
            if (injury) {
                await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);

                dialogue.injury = injury.name;
                dialogue.injuryTier = injury.tier || undefined;

                NotificationService.NotifyUser(currentUser.id, NotificationTypes.hospitalised, {
                    reason: "explore_character_encounter",
                    dialogue: dialogue,
                    injury: injury.name,
                    injuryTier: injury.tier,
                });

                logAction({
                    action: "EXPLORE_CHARACTER_ENCOUNTER_INJURED",
                    userId: currentUser.id,
                    info: {
                        injury: injury.name,
                        injuryTier: injury.tier,
                        location: location,
                    },
                });
            }
        } else if (Math.random() < jailChance) {
            // Jail chance if not hospitalized
            const jailShrineBuffActive = (await ShrineHelper.dailyBuffIsActive("jail", currentUser.id)) || 1;
            dialogue.jailed = true;
            updateValues.jailedUntil = BigInt(
                Date.now() + CHARACTER_ENCOUNTER_CONFIG.DEFAULT_ENCOUNTER_JAIL_DURATION_MS * jailShrineBuffActive
            );
            updateValues.jailReason = "explore";

            logAction({
                action: "EXPLORE_CHARACTER_ENCOUNTER_JAILED",
                userId: currentUser.id,
                info: {
                    jailDuration: CHARACTER_ENCOUNTER_CONFIG.DEFAULT_ENCOUNTER_JAIL_DURATION_MS * jailShrineBuffActive,
                    location: location,
                },
            });
        }
    }

    // Apply user updates
    if (Object.keys(updateValues).length > 0) {
        await UserService.updateUser(currentUser.id, updateValues);
    }

    return {
        success: true,
        message: goodOutcome
            ? `You had a pleasant encounter with ${dialogue.character}!`
            : `You were mugged by ${dialogue.character}!`,
        data: {
            action: "character_encounter",
            dialogue: dialogue,
            goodOutcome: goodOutcome,
        },
    };
};
