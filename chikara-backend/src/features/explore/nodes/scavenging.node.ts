import * as InventoryService from "../../../core/inventory.service.js";
import * as NotificationService from "../../../core/notification.service.js";
import * as StatusEffectService from "../../../core/statuseffect.service.js";
import * as UserService from "../../../core/user.service.js";
import getScavengeLocation, { type InjuryType, type ScavengeLocation } from "../../../data/scavengeLocations.js";
import * as ShrineHelper from "../../shrine/shrine.helpers.js";
import { logAction } from "../../../lib/actionLogger.js";
import { db } from "../../../lib/db.js";
import type { ItemModel, StatusEffectModel, UserModel } from "../../../lib/db.js";
import { NotificationTypes } from "../../../types/notification.js";
import type { ExploreNodeLocation } from "@prisma/client";
import { mapExploreLocationToLocationTypes } from "../explore.helpers.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
import * as UserRepository from "../../../repositories/user.repository.js";
import { emitResourceGathered } from "../../../core/events/game-event.service.js";

/**
 * Scavenging node configuration
 */
export const SCAVENGING_CONFIG = {
    FAIL_CHANCE: 0.3, // 30% chance of bad outcome
    JAIL_CHANCE: 0.2, // 20% chance to be jailed / 80% chance to get an injury on bad outcome
    DEFAULT_JAIL_DURATION_MS: 10 * 60 * 1000, // 10 minutes
    SCAVENGE_CHOICES: ["trash", "medical", "upgrade", "herb", "tech", "ore"] as const,
    SCAVENGE_TIMEOUT_MS: 5 * 60 * 1000, // 5 minutes to make choice
} as const;

/**
 * Interface for scavenging result data
 */
export interface ScavengeResult {
    success: boolean;
    choice: string;
    itemReward?: Omit<ItemModel, "createdAt" | "updatedAt">;
    itemQuantity?: number;
    jailed?: boolean;
    jailDuration?: number;
    injury?: StatusEffectModel;
    scavengeData?: ScavengeLocation;
    message?: string;
}

/**
 * Interface for scavenging choices
 */
export interface ScavengeChoices {
    choices: string[];
    scavengeValidUntil: number;
    scavengeData: ScavengeLocation;
}

/**
 * Get potential drops for scavenging in explore mode
 */
export const findExploreScavengeDrops = async (
    userLevel: number,
    location: ExploreNodeLocation,
    scavengeType: string
) => {
    const mappedLocation = mapExploreLocationToLocationTypes(location);

    return await db.drop_chance.findMany({
        where: {
            dropChanceType: "scavenge",
            scavengeType,
            location: {
                in: [mappedLocation, "any"],
            },
            minLevel: {
                lte: userLevel,
            },
            maxLevel: {
                gte: userLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: {
                omit: {
                    createdAt: true,
                    updatedAt: true,
                },
            },
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};

/**
 * Handle successful scavenging outcome - gives items
 */
export const handleScavengeSuccess = async (user: UserModel, location: ExploreNodeLocation, choice: string) => {
    const potentialDrops = await findExploreScavengeDrops(user.level, location, choice);

    if (!potentialDrops || potentialDrops.length === 0) {
        return null;
    }

    // Pick a random item from the potential drops
    const randomIndex: number = Math.floor(Math.random() * potentialDrops.length);
    const selectedItem = potentialDrops[randomIndex];

    // Check if itemId and item are not null
    if (selectedItem.itemId === null || !selectedItem.item) {
        return null;
    }

    // Apply shrine gatheringAmount buff if active
    let finalQuantity = selectedItem.quantity;
    const gatheringAmountBuff = await ShrineHelper.dailyBuffIsActive("gatheringAmount", user.id);
    if (gatheringAmountBuff) {
        finalQuantity = Math.round(finalQuantity * gatheringAmountBuff);
    }

    // Add the item to the user's inventory
    await InventoryService.AddItemToUser({
        userId: user.id,
        itemId: selectedItem.itemId,
        amount: finalQuantity,
        isTradeable: true,
    });

    // Emit resource gathering event for quest objectives
    await emitResourceGathered({
        userId: user.id,
        itemId: selectedItem.itemId,
        quantity: finalQuantity,
        activityType: "scavenging",
        location: mapExploreLocationToLocationTypes(location),
    });

    logAction({
        action: "EXPLORE_SCAVENGE_ITEM",
        userId: user.id,
        info: {
            itemId: selectedItem.item.id,
            itemName: selectedItem.item.name,
            quantity: finalQuantity,
            location: location,
            choice: choice,
        },
    });

    return {
        itemReward: selectedItem.item,
        itemQuantity: finalQuantity,
    };
};

/**
 * Handle failed scavenging outcome - applies injury or jail
 */
export const handleScavengeFailure = async (
    currentUser: UserModel,
    location: ExploreNodeLocation,
    choice: string,
    scavengeChoices: string[]
) => {
    const outcome: { jailed?: boolean; jailDuration?: number; injury?: StatusEffectModel } = {};

    if (Math.random() <= SCAVENGING_CONFIG.JAIL_CHANCE) {
        // Send user to jail
        const jailShrineBuffActive = (await ShrineHelper.dailyBuffIsActive("jail", currentUser.id)) || 1;
        const jailDuration: number = SCAVENGING_CONFIG.DEFAULT_JAIL_DURATION_MS * jailShrineBuffActive;

        await UserService.JailUser(currentUser.id, jailDuration, "Caught scavenging", {
            notificationType: "jail",
        });

        outcome.jailed = true;
        outcome.jailDuration = jailDuration;

        logAction({
            action: "EXPLORE_SCAVENGE_JAILED",
            userId: currentUser.id,
            info: {
                jailDuration: jailDuration,
                location: location,
                choice: choice,
            },
        });

        NotificationService.NotifyUser(
            currentUser.id,
            NotificationTypes.jail,
            {
                reason: "scavenging",
                duration: jailDuration,
            },
            true
        );
    } else {
        // Apply injury
        const scavengeData: ScavengeLocation = getScavengeLocation(scavengeChoices);

        // Make sure choice exists in choices
        if (!scavengeData.choices[choice]) {
            throw new Error(`Invalid scavenge choice: ${choice}`);
        }

        const injuryType: InjuryType = scavengeData.choices[choice].injury;

        // Apply injury to user
        const injury = await StatusEffectService.GetRandomInjury("Minor", injuryType);
        if (!injury) {
            throw new Error(`No minor injury found for type: ${injuryType}`);
        }
        await StatusEffectService.ApplyStatusEffectToUser(currentUser, injury);

        outcome.injury = injury;

        NotificationService.NotifyUser(
            currentUser.id,
            NotificationTypes.injured,
            {
                reason: "scavenging",
                injury: injury.name,
                injuryTier: injury.tier,
            },
            true
        );

        logAction({
            action: "EXPLORE_SCAVENGE_INJURY",
            userId: currentUser.id,
            info: {
                injury: injury.name,
                injuryTier: injury.tier,
                location: location,
                choice: choice,
            },
        });
    }

    return outcome;
};

/**
 * Initialize scavenging node - provides choices to the user
 */
export const initializeScavenging = async (userId: number, location: ExploreNodeLocation) => {
    const scavengeChoices = [...SCAVENGING_CONFIG.SCAVENGE_CHOICES];

    // Get the first random choice and remove it from the array
    const scavengeChoice1Index: number = Math.floor(Math.random() * scavengeChoices.length);
    const scavengeChoice1 = scavengeChoices[scavengeChoice1Index];
    scavengeChoices.splice(scavengeChoice1Index, 1); // Remove the chosen item

    // Get the second random choice from the remaining choices
    const scavengeChoice2 = scavengeChoices[Math.floor(Math.random() * scavengeChoices.length)];

    const choices = [scavengeChoice1, scavengeChoice2];
    const scavengeValidUntil = Date.now() + SCAVENGING_CONFIG.SCAVENGE_TIMEOUT_MS;
    const scavengeData = getScavengeLocation(choices);

    logAction({
        action: "EXPLORE_SCAVENGE_INITIATED",
        userId: userId,
        info: {
            location: location,
            choices: choices,
            scavengeLocation: scavengeData.location,
        },
    });

    return {
        choices,
        scavengeValidUntil,
        scavengeData,
    };
};

/**
 * Handle scavenging choice made by user
 */
export const processScavengeChoice = async (
    userId: number,
    nodeId: number,
    location: ExploreNodeLocation,
    choiceIndex: number,
    currentChoices: string[]
) => {
    if (choiceIndex !== 1 && choiceIndex !== 2) {
        return {
            success: false,
            message: "Invalid choice. Please select 1 or 2.",
        };
    }

    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return {
            success: false,
            message: "User not found",
        };
    }

    if (!currentChoices || currentChoices.length < 2) {
        return {
            success: false,
            message: "No scavenging choices available",
        };
    }

    const selectedChoice = currentChoices[choiceIndex - 1];
    const goodOutcome: boolean = Math.random() > SCAVENGING_CONFIG.FAIL_CHANCE;

    const scavengeData = getScavengeLocation(currentChoices);
    const result: ScavengeResult = {
        success: goodOutcome,
        choice: selectedChoice,
        scavengeData,
    };

    if (goodOutcome) {
        // Success - get item reward
        const itemResult = await handleScavengeSuccess(currentUser, location, selectedChoice);
        if (itemResult) {
            result.itemReward = itemResult.itemReward;
            result.itemQuantity = itemResult.itemQuantity;
            result.message = scavengeData.choices[selectedChoice].success;
        } else {
            result.message = "You searched thoroughly but didn't find anything useful.";
        }
    } else {
        // Failure - get injury or jail
        const failureResult = await handleScavengeFailure(currentUser, location, selectedChoice, currentChoices);
        result.jailed = failureResult.jailed;
        result.jailDuration = failureResult.jailDuration;
        result.injury = failureResult.injury;

        if (failureResult.jailed) {
            result.message = scavengeData.choices[selectedChoice].failureJail;
        } else {
            result.message = scavengeData.choices[selectedChoice].failureInjury;
        }
    }

    return {
        success: true,
        message: "Scavenging completed",
        data: result,
    };
};

/**
 * Main handler for scavenging encounters in explore nodes
 * This handles the initial interaction with a scavenging node
 */
export const handleScavengingEncounter = async (userId: number, nodeId: number, location: ExploreNodeLocation) => {
    try {
        // Initialize the scavenging encounter
        const scavengeChoices = await initializeScavenging(userId, location);

        // Update the node metadata with the choices and put it in "current" status
        const metadataUpdated = await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
            choices: scavengeChoices.choices,
            scavengeValidUntil: scavengeChoices.scavengeValidUntil,
            scavengeData: scavengeChoices.scavengeData,
        });

        if (!metadataUpdated) {
            return {
                success: false,
                message: "Failed to initialize scavenging",
            };
        }

        return {
            success: true,
            message: "Choose your scavenging approach",
            data: {
                action: "scavenge_choices",
                nodeId,
                ...scavengeChoices,
            },
        };
    } catch {
        return {
            success: false,
            message: "Failed to initialize scavenging encounter",
        };
    }
};
