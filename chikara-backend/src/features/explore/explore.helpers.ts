import type { ExploreNodeLocation, ExploreNodeType, LocationTypes, TravelMethod } from "@prisma/client";
import { logger, handleError, tryCatch } from "../../utils/log.js";
import {
    BASE_NODE_CONFIG,
    DISABLED_NODE_TYPES,
    GRID_CONFIG,
    LOCATION_NODE_COUNTS,
    LOCATION_NODE_WEIGHTS,
    PLAYER_NODE_CONFIG,
    STATIC_NODE_RESERVED_POSITIONS,
    TRAVEL_COSTS,
    TRAVEL_TIMES,
} from "./explore.constants.js";
import * as ExploreRepository from "../../repositories/explore.repository.js";
import type { MapNodePosition } from "./explore.types.js";
import { handleBattleEncounter } from "./nodes/battle.node.js";
import { type ExploreCharacterDialogue, handleCharacterEncounter } from "./nodes/character_encounter.node.js";
import { handleForagingEncounter } from "./nodes/foraging.node.js";
import { handleMiningEncounter } from "./nodes/mining.node.js";
import { handleScavengingEncounter } from "./nodes/scavenging.node.js";
import * as ShrineHelper from "../shrine/shrine.helpers.js";
import { handleStoryNodeInteraction, createStoryNodesForUser } from "./nodes/story.node.js";

/**
 * Map ExploreNodeLocation to LocationTypes for database queries
 */
export const mapExploreLocationToLocationTypes = (location: ExploreNodeLocation): LocationTypes => {
    // Map explore locations to general location types based on district characteristics
    switch (location) {
        case "shibuya": {
            // Youth/nightlife district
            return "alley";
        }
        case "shinjuku": {
            // Business district
            return "mall";
        }
        case "bunkyo": {
            // Educational district
            return "school";
        }
        case "chiyoda": {
            // Government/imperial district
            return "shrine";
        }
        case "minato": {
            // Port district
            return "any";
        }
        default: {
            return "any";
        }
    }
};

/**
 * Calculate Manhattan distance between two grid positions
 */
const calculateManhattanDistance = (pos1: MapNodePosition, pos2: MapNodePosition): number => {
    return Math.abs(pos1.x - pos2.x) + Math.abs(pos1.y - pos2.y);
};

/**
 * Generate all possible grid positions for the 5x5 grid
 */
const generateAllGridPositions = (): MapNodePosition[] => {
    const positions: MapNodePosition[] = [];
    for (let x = GRID_CONFIG.MIN_COORD; x <= GRID_CONFIG.MAX_COORD; x++) {
        for (let y = GRID_CONFIG.MIN_COORD; y <= GRID_CONFIG.MAX_COORD; y++) {
            positions.push({ x, y });
        }
    }
    return positions;
};

/**
 * Check if a position is reserved for static nodes in the given location
 */
const isPositionReserved = (position: MapNodePosition, location: ExploreNodeLocation): boolean => {
    const reservedPositions = STATIC_NODE_RESERVED_POSITIONS[location];
    return reservedPositions.some((reserved) => reserved.x === position.x && reserved.y === position.y);
};

/**
 * Get the reserved grid positions for static nodes in a specific location
 */
export const getStaticNodeReservedPositions = (location: ExploreNodeLocation): MapNodePosition[] => {
    return [...STATIC_NODE_RESERVED_POSITIONS[location]];
};

/**
 * Validate that a position is within the 5x5 grid bounds
 */
export const isValidGridPosition = (position: MapNodePosition): boolean => {
    return (
        position.x >= GRID_CONFIG.MIN_COORD &&
        position.x <= GRID_CONFIG.MAX_COORD &&
        position.y >= GRID_CONFIG.MIN_COORD &&
        position.y <= GRID_CONFIG.MAX_COORD
    );
};

/**
 * Validate travel parameters to ensure they exist in the travel configuration
 * Throws an error if invalid
 */
export const validateTravelParameters = (method: TravelMethod, location: ExploreNodeLocation): void => {
    // Check if method exists in TRAVEL_COSTS
    if (!(method in TRAVEL_COSTS)) {
        return handleError(`Invalid travel method: ${method}`, 400);
    }

    // Check if method exists in TRAVEL_TIMES
    if (!(method in TRAVEL_TIMES)) {
        return handleError(`Travel method ${method} has no time configuration`, 400);
    }

    // Check if location exists for the given method in TRAVEL_COSTS
    if (!(location in TRAVEL_COSTS[method])) {
        return handleError(`Location ${location} is not available for travel method ${method}`, 400);
    }

    // Check if location exists for the given method in TRAVEL_TIMES
    if (!(location in TRAVEL_TIMES[method])) {
        return handleError(`Location ${location} has no travel time configured for method ${method}`, 400);
    }

    // Additional safety check for undefined values
    const cost = TRAVEL_COSTS[method][location];
    const travelTime = TRAVEL_TIMES[method][location];

    if (cost === undefined) {
        return handleError(`Travel cost is undefined for ${location} via ${method}`, 400);
    }

    if (travelTime === undefined) {
        return handleError(`Travel time is undefined for ${location} via ${method}`, 400);
    }
};

/**
 * Get the next available reserved position for a static node in the given location
 * Returns null if all reserved positions are taken
 */
export const getNextStaticNodePosition = async (location: ExploreNodeLocation): Promise<MapNodePosition | null> => {
    const reservedPositions = getStaticNodeReservedPositions(location);
    const existingStaticNodes = await ExploreRepository.getStaticNodesByLocation(location);
    const occupiedPositions = existingStaticNodes.map((node) => node.position);

    // Find the first reserved position that's not occupied
    for (const reservedPos of reservedPositions) {
        const isOccupied = occupiedPositions.some(
            (occupied) => occupied.x === reservedPos.x && occupied.y === reservedPos.y
        );

        if (!isOccupied) {
            return reservedPos;
        }
    }

    return null; // All reserved positions are taken
};

/**
 * Generate a grid-based position that maximizes spacing from existing nodes
 * Uses a 5x5 grid system (coordinates 0-4) and prevents clustering
 */
export const generateRandomPosition = async (
    userId: number,
    location: ExploreNodeLocation
): Promise<MapNodePosition | null> => {
    // Get existing nodes in this location
    const [existingPlayerNodes, existingStaticNodes] = await Promise.all([
        ExploreRepository.getActivePlayerNodesByLocation(userId, location),
        ExploreRepository.getStaticNodesByLocation(location),
    ]);

    // Combine all existing positions
    const existingPositions = [
        ...existingPlayerNodes.map((node) => node.position),
        ...existingStaticNodes.map((node) => node.position),
    ];

    // Generate all possible grid positions
    const allPositions = generateAllGridPositions();

    // Filter out positions that are already occupied or reserved for static nodes
    const availablePositions = allPositions.filter((position) => {
        // Check if position is already occupied
        const isOccupied = existingPositions.some((existing) => existing.x === position.x && existing.y === position.y);

        // Check if position is reserved for static nodes
        const isReserved = isPositionReserved(position, location);

        return !isOccupied && !isReserved;
    });

    // If no positions are available, return null
    if (availablePositions.length === 0) {
        return null;
    }

    // If only one position is available, return it
    if (availablePositions.length === 1) {
        return availablePositions[0];
    }

    // Calculate the minimum distance to existing nodes for each available position
    const positionsWithDistances = availablePositions.map((position) => {
        let minDistance = Infinity;

        for (const existingPos of existingPositions) {
            const distance = calculateManhattanDistance(position, existingPos);
            minDistance = Math.min(minDistance, distance);
        }

        return {
            position,
            minDistance: existingPositions.length === 0 ? Infinity : minDistance,
        };
    });

    // Find the maximum minimum distance (positions that are farthest from all existing nodes)
    const maxMinDistance = Math.max(...positionsWithDistances.map((p) => p.minDistance));

    // Filter positions that have the maximum minimum distance
    const bestPositions = positionsWithDistances.filter((p) => p.minDistance === maxMinDistance).map((p) => p.position);

    // Randomly select one of the best positions
    const randomIndex = Math.floor(Math.random() * bestPositions.length);
    return bestPositions[randomIndex];
};

/**
 * Select a random node type based on location-specific weights
 */
export const selectRandomNodeType = (location: ExploreNodeLocation): keyof typeof BASE_NODE_CONFIG => {
    const locationWeights = LOCATION_NODE_WEIGHTS[location];
    if (!locationWeights) {
        logger.warn(`No weights configured for location ${location}, using fallback`);
        return "CHARACTER_ENCOUNTER";
    }

    // Filter out disabled node types
    const enabledWeights: Record<string, number> = {};
    for (const [nodeType, weight] of Object.entries(locationWeights)) {
        if (!DISABLED_NODE_TYPES[nodeType as keyof typeof DISABLED_NODE_TYPES]) {
            enabledWeights[nodeType] = weight;
        }
    }

    // Check if we have any enabled node types
    if (Object.keys(enabledWeights).length === 0) {
        logger.warn(`All node types are disabled for location ${location}, using CHARACTER_ENCOUNTER fallback`);
        return "CHARACTER_ENCOUNTER";
    }

    const totalWeight = Object.values(enabledWeights).reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;

    for (const [nodeType, weight] of Object.entries(enabledWeights)) {
        random -= weight;
        if (random <= 0) {
            return nodeType as keyof typeof BASE_NODE_CONFIG;
        }
    }

    // If we somehow get here, return the first enabled node type
    return Object.keys(enabledWeights)[0] as keyof typeof BASE_NODE_CONFIG;
};

/**
 * Generate random content for a node type
 */
export const generateNodeContent = (nodeType: keyof typeof BASE_NODE_CONFIG) => {
    const config = BASE_NODE_CONFIG[nodeType];
    const title = config.titles[Math.floor(Math.random() * config.titles.length)];
    const description = config.descriptions[Math.floor(Math.random() * config.descriptions.length)];

    let metadata: Record<string, unknown> = {};

    // Add type-specific metadata
    switch (nodeType) {
        case "BATTLE": {
            // 5% chance for a battle node to be a miniboss
            const isMiniboss = Math.random() < 0.05;
            metadata = {
                creatureLevel: Math.floor(Math.random() * 10) + 1,
                isMiniboss,
            };
            break;
        }
        case "CHARACTER_ENCOUNTER": {
            break;
        }
        case "MINING_NODE": {
            // Add mining-specific metadata
            const miningDifficulties = ["easy", "medium", "hard"] as const;
            const difficulty = miningDifficulties[Math.floor(Math.random() * miningDifficulties.length)];
            let estimatedReward: string;
            switch (difficulty) {
                case "easy": {
                    estimatedReward = "low";
                    break;
                }
                case "medium": {
                    estimatedReward = "medium";
                    break;
                }
                case "hard": {
                    estimatedReward = "high";
                    break;
                }
            }
            metadata = {
                difficulty,
                estimatedReward,
            };
            break;
        }
    }

    return { title, description, metadata };
};

/**
 * Generate new player nodes if needed (for all locations or a specific location)
 */
export const generatePlayerNodesIfNeeded = async (userId: number, location: ExploreNodeLocation): Promise<void> => {
    // Get the base target node count for this location
    let targetNodeCount = LOCATION_NODE_COUNTS[location];
    if (!targetNodeCount) {
        logger.warn(`No node count configured for location ${location}`);
        return;
    }

    // Apply shrine exploreSpawn buff if active
    const exploreSpawnBuff = await ShrineHelper.dailyBuffIsActive("exploreSpawn");
    if (exploreSpawnBuff) {
        targetNodeCount = Math.round(targetNodeCount * exploreSpawnBuff);
    }

    // Check if there are any enabled node types for this location
    if (!hasEnabledNodeTypesForLocation(location)) {
        logger.warn(`All node types are disabled for location ${location}, skipping node generation`);
        return;
    }

    // Get current node count for this location
    const currentNodeCount = await ExploreRepository.countPlayerNodesByLocation(userId, location);

    // Generate nodes to reach the target count
    if (currentNodeCount < targetNodeCount) {
        const nodesToGenerate = targetNodeCount - currentNodeCount;

        logger.info(
            `Generating ${nodesToGenerate} new nodes for user ${userId} in ${location} (current: ${currentNodeCount}, target: ${targetNodeCount})`
        );

        for (let i = 0; i < nodesToGenerate; i++) {
            const nodeType = selectRandomNodeType(location);
            const { title, description, metadata } = generateNodeContent(nodeType);
            const position = await generateRandomPosition(userId, location);

            // Skip this node if no position is available
            if (!position) {
                logger.warn(`No available grid positions for user ${userId} in ${location}, skipping node generation`);
                continue;
            }

            await ExploreRepository.createPlayerNode(
                userId,
                nodeType as ExploreNodeType,
                title,
                description,
                position,
                location,
                PLAYER_NODE_CONFIG.DEFAULT_EXPIRATION_HOURS,
                metadata
            );
        }
    }

    // Always try to create story nodes for the user in this location
    // This ensures quest-driven story nodes are properly created
    await tryCatch(async () => {
        await createStoryNodesForUser(userId, location);
        logger.info(`Successfully processed story nodes for user ${userId} in ${location}`);
    });
};

/**
 * Handle node interaction based on node type
 */
export const handleNodeInteraction = async (
    nodeType: ExploreNodeType,
    metadata: Record<string, unknown> | undefined,
    userId: number,
    nodeId: number,
    isStatic: boolean,
    location: ExploreNodeLocation
) => {
    logger.info(`User ${userId} interacting with ${nodeType} node ${nodeId} (static: ${isStatic}) at ${location}`);

    switch (nodeType) {
        case "BATTLE": {
            const battleResult = await handleBattleEncounter(
                userId,
                metadata as { isMiniboss: boolean; creatureLevel: number } | undefined,
                location
            );

            if (battleResult && "error" in battleResult) {
                return {
                    success: false,
                    message: battleResult.error,
                };
            }

            // Battle was successfully initiated (encounterResult is null)
            return {
                success: true,
                message: "Battle initiated!",
                data: {
                    action: "battle",
                    creatureLevel: metadata?.creatureLevel || 1,
                    isMiniboss: metadata?.isMiniboss || false,
                },
            };
        }

        case "CHARACTER_ENCOUNTER": {
            const encounterResult = await handleCharacterEncounter(userId, nodeId, location);

            if (!encounterResult.success) {
                return {
                    success: false,
                    message: encounterResult.message || "Character encounter failed",
                };
            }

            const encounterData = encounterResult.data as {
                action: string;
                dialogue: ExploreCharacterDialogue;
                healed?: boolean;
                goodOutcome?: boolean;
            };

            await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
                ...encounterData,
                action: undefined,
            });

            return {
                success: true,
                message: encounterResult.message || "Character encounter completed",
                data: encounterResult.data,
            };
        }

        case "ACTION": {
            return {
                success: true,
                message: "You discovered something interesting!",
                data: {
                    action: "discovery",
                    type: "action",
                },
            };
        }

        case "SCAVENGE_NODE": {
            const scavengeResult = await handleScavengingEncounter(userId, nodeId, location);

            if (!scavengeResult.success) {
                return {
                    success: false,
                    message: scavengeResult.message || "Scavenging failed",
                };
            }

            // Put the node into "current" status so the user can make their choice
            await ExploreRepository.updatePlayerNodeStatus(nodeId, userId, "current");

            return {
                success: true,
                message: scavengeResult.message || "Scavenging opportunity found",
                data: scavengeResult.data,
            };
        }

        case "MINING_NODE": {
            const miningResult = await handleMiningEncounter(userId, nodeId, location);

            if (!miningResult.success) {
                return {
                    success: false,
                    message: miningResult.message || "Mining failed",
                };
            }

            // Put the node into "current" status so the user can start mining
            await ExploreRepository.updatePlayerNodeStatus(nodeId, userId, "current");

            return {
                success: true,
                message: miningResult.message || "Mining site prepared",
                data: miningResult.data,
            };
        }

        case "FORAGING_NODE": {
            const foragingResult = await handleForagingEncounter(userId, nodeId, location);

            if (!foragingResult.success) {
                return {
                    success: false,
                    message: foragingResult.message || "Foraging failed",
                };
            }

            // Put the node into "current" status so the user can start foraging
            await ExploreRepository.updatePlayerNodeStatus(nodeId, userId, "current");

            return {
                success: true,
                message: foragingResult.message || "Foraging area prepared",
                data: foragingResult.data,
            };
        }

        case "STORY": {
            const storyResult = await handleStoryNodeInteraction(userId, nodeId, location, metadata || {});

            if (!storyResult.success) {
                return {
                    success: false,
                    message: storyResult.message,
                };
            }

            // Put the node into "current" status so the story episode player can be displayed
            await ExploreRepository.updatePlayerNodeStatus(nodeId, userId, "current");
            await ExploreRepository.updatePlayerNodeMetadata(nodeId, userId, {
                ...metadata,
                episodeData: storyResult.episodeData,
            });

            return {
                success: true,
                message: storyResult.message,
                data: {
                    action: "story",
                    redirectToStoryPlayer: storyResult.redirectToStoryPlayer,
                    episodeData: storyResult.episodeData,
                },
            };
        }

        default: {
            return {
                success: false,
                message: "Unknown node type.",
            };
        }
    }
};

/**
 * Check if a specific node type is currently disabled
 */
export const isNodeTypeDisabled = (nodeType: string): boolean => {
    return DISABLED_NODE_TYPES[nodeType as keyof typeof DISABLED_NODE_TYPES] || false;
};

/**
 * Check if any node types are enabled for a specific location
 */
export const hasEnabledNodeTypesForLocation = (location: ExploreNodeLocation): boolean => {
    const locationWeights = LOCATION_NODE_WEIGHTS[location];
    if (!locationWeights) return false;

    return Object.keys(locationWeights).some(
        (nodeType) => !DISABLED_NODE_TYPES[nodeType as keyof typeof DISABLED_NODE_TYPES]
    );
};
