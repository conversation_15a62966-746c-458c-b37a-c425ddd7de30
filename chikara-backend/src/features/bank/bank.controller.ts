import gameConfig from "../../config/gameConfig.js";
import * as NotificationService from "../../core/notification.service.js";
import * as UserService from "../../core/user.service.js";
import * as BankRepository from "../../repositories/bank.repository.js";
import {
    DepositParams,
    TransactionHistory,
    TransactionHistoryParams,
    TransactionHistoryResponse,
    TransactionResponse,
    TransferParams,
    WithdrawParams,
} from "./bank.types.js";
import { logAction } from "../../lib/actionLogger.js";
import { NotificationTypes } from "../../types/notification.js";
import { LogErrorStack } from "../../utils/log.js";
import * as UserRepository from "../../repositories/user.repository.js";

export const deposit = async ({ userId, amount }: DepositParams): Promise<TransactionResponse> => {
    try {
        if (gameConfig.DEPOSIT_DISABLED) {
            return { error: "Deposits are disabled", statusCode: 400 };
        }
        //TODO: cope with races (async-mutex?)
        const depositAmount = amount;

        const currentUser = await UserRepository.getUserById(userId);

        if (
            !currentUser ||
            Number.isNaN(depositAmount) ||
            depositAmount < gameConfig.MINIMUM_DEPOSIT ||
            (currentUser.cash || 0) < depositAmount
        ) {
            return { error: "Invalid deposit amount", statusCode: 400 };
        }

        const transactionFee = Math.ceil(depositAmount * gameConfig.TRANSACTION_FEE);

        // Update user with Prisma
        const updatedUser = await UserService.updateUser(userId, {
            bank_balance: {
                increment: depositAmount - transactionFee,
            },
            cash: {
                decrement: depositAmount,
            },
        });

        logAction({
            action: "BANK_DEPOSIT",
            userId: currentUser.id,
            info: {
                amount: depositAmount,
                fee: transactionFee,
            },
        });

        // Create transaction record with Prisma
        await BankRepository.createBankTransaction({
            transaction_type: "bank_deposit",
            cash: depositAmount,
            transactionFee: transactionFee,
            initiator: {
                connect: { id: currentUser.id },
            },
            initiatorCashBalance: updatedUser.cash || 0,
            initiatorBankBalance: updatedUser.bank_balance || 0,
        });

        return {
            data: {
                balance: updatedUser.bank_balance || 0,
                cash: updatedUser.cash || 0,
                fee: transactionFee,
            },
        };
    } catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};

export const withdraw = async ({ userId, amount }: WithdrawParams): Promise<TransactionResponse> => {
    //TODO: cope with races (async-mutex?)
    try {
        const withdrawAmount = amount;

        const currentUser = await UserRepository.getUserById(userId);

        if (
            !currentUser ||
            Number.isNaN(withdrawAmount) ||
            withdrawAmount < gameConfig.MINIMUM_WITHDRAWAL ||
            (currentUser.bank_balance || 0) < withdrawAmount
        ) {
            return { error: "Invalid withdrawl amount", statusCode: 400 };
        }

        // Update user with Prisma
        const updatedUser = await UserService.updateUser(userId, {
            bank_balance: {
                decrement: withdrawAmount,
            },
            cash: {
                increment: withdrawAmount,
            },
        });

        // Create transaction record with Prisma
        await BankRepository.createBankTransaction({
            transaction_type: "bank_withdrawal",
            cash: withdrawAmount,
            transactionFee: 0, // no tx fee for withdrawals
            initiator: {
                connect: { id: currentUser.id },
            },
            initiatorCashBalance: updatedUser.cash || 0,
            initiatorBankBalance: updatedUser.bank_balance || 0,
        });

        logAction({
            action: "BANK_WITHDRAWAL",
            userId: currentUser.id,
            info: {
                amount: withdrawAmount,
                fee: 0,
            },
        });

        return {
            data: {
                balance: updatedUser.bank_balance || 0,
                cash: updatedUser.cash || 0,
                fee: 0,
            },
        };
    } catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};

export const transfer = async ({
    userId,
    recipientId,
    transferAmount,
}: TransferParams): Promise<TransactionResponse> => {
    try {
        if (recipientId === userId) {
            return { error: "Can't transfer money to yourself!", statusCode: 400 };
        }

        const transferAmountNum = transferAmount;

        const currentUser = await UserRepository.getUserById(userId);

        if (
            !currentUser ||
            Number.isNaN(transferAmountNum) ||
            transferAmountNum < gameConfig.MINIMUM_TRANSFER ||
            (currentUser.bank_balance || 0) < transferAmountNum
        ) {
            return { error: "Invalid transfer amount", statusCode: 400 };
        }

        const transactionFee = Math.ceil(transferAmountNum * gameConfig.TRANSACTION_FEE);

        const recipient = await UserRepository.getUserById(recipientId);

        if (!recipient) {
            return { error: "User does not exist", statusCode: 400 };
        }

        // Update both users with Prisma in a transaction
        const [updatedCurrentUser, updatedRecipient] = await BankRepository.updateBothUsersTransaction(
            userId,
            recipientId,
            transferAmountNum,
            transactionFee
        );

        NotificationService.NotifyUser(currentUser.id, NotificationTypes.transfer_sent, {
            user: recipient.id,
            amount: transferAmountNum,
            fee: transactionFee,
        });

        NotificationService.NotifyUser(recipient.id, NotificationTypes.transfer_received, {
            user: currentUser.id,
            amount: transferAmountNum,
        });

        logAction({
            action: "BANK_TRANSFER",
            userId: currentUser.id,
            info: {
                amount: transferAmountNum,
                fee: transactionFee,
                recipientId: recipient.id,
            },
        });

        // Create transaction record with Prisma

        await BankRepository.createBankTransaction({
            transaction_type: "bank_transfer",
            cash: transferAmountNum,
            transactionFee: transactionFee,
            initiator: {
                connect: { id: currentUser.id },
            },
            secondParty: {
                connect: { id: recipient.id },
            },
            initiatorCashBalance: updatedCurrentUser.cash || 0,
            initiatorBankBalance: updatedCurrentUser.bank_balance || 0,
            secondPartyCashBalance: updatedRecipient.cash || 0,
            secondPartyBankBalance: updatedRecipient.bank_balance || 0,
        });

        return {
            data: {
                balance: updatedCurrentUser.bank_balance || 0,
                cash: updatedCurrentUser.cash || 0,
                fee: transactionFee,
            },
        };
    } catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};

export const getTransactionHistory = async ({
    userId,
}: TransactionHistoryParams): Promise<TransactionHistoryResponse> => {
    try {
        const transactions = await BankRepository.getTransactionHistory(userId, gameConfig.TRANSACTION_HISTORY_LIMIT);

        const values = transactions.map((transaction) => {
            const value: TransactionHistory = {
                transaction_type: transaction.transaction_type,
                cash: transaction.cash,
                transactionFee: transaction.transactionFee,
                initiatorId: transaction.initiatorId,
                secondPartyId: transaction.secondPartyId,
                createdAt: transaction.createdAt,
            };

            // make sure we only ever show the correct user balance, including for second party transactions
            if (transaction.initiatorId === userId) {
                value.cashBalance = transaction.initiatorCashBalance;
                value.bankBalance = transaction.initiatorBankBalance;
            } else {
                value.cashBalance = transaction.secondPartyCashBalance ?? undefined;
                value.bankBalance = transaction.secondPartyBankBalance ?? undefined;
            }

            return value;
        });

        return { data: values };
    } catch (error) {
        LogErrorStack({ error });
        throw error;
    }
};
