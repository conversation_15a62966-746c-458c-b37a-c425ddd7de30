import gameConfig from "../../../config/gameConfig.js";
import { donateToShrineSchema } from "../shrine.validation.js";
import { describe, expect, it } from "vitest";

describe("Shrine Validation", () => {
    describe("donateToShrineSchema", () => {
        it("should validate valid donation amount", () => {
            const validData = { amount: gameConfig.SHRINE_MINIMUM_DONATION + 100 };
            const result = donateToShrineSchema.safeParse(validData);
            expect(result.success).toBe(true);
        });

        it("should reject negative amounts", () => {
            const invalidData = { amount: -100 };
            const result = donateToShrineSchema.safeParse(invalidData);
            expect(result.success).toBe(false);
        });

        it("should reject amounts below minimum donation", () => {
            const invalidData = { amount: gameConfig.SHRINE_MINIMUM_DONATION - 1 };
            const result = donateToShrineSchema.safeParse(invalidData);
            expect(result.success).toBe(false);
        });

        it("should reject non-integer amounts", () => {
            const invalidData = { amount: 100.5 };
            const result = donateToShrineSchema.safeParse(invalidData);
            expect(result.success).toBe(false);
        });

        it("should reject missing amount", () => {
            const invalidData = {};
            const result = donateToShrineSchema.safeParse(invalidData);
            expect(result.success).toBe(false);
        });
    });
});
