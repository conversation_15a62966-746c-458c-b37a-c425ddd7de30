/* eslint-disable unicorn/no-useless-switch-case */
import * as UserService from "../../../core/user.service.js";
import { logAction } from "../../../lib/actionLogger.js";
import { ExtUserModel } from "../../../lib/db.js";
import { Prisma } from "@prisma/client";
import type { BattleType, NPCUser, RooftopNPCUser } from "../types/battle.types.js";
import { createBattleState, generateBattleId, sanitizeBattleStateForFrontend } from "../battle.state.js";
import gameConfig from "../../../config/gameConfig.js";
import * as BattleHelpers from "../helpers/battle.helpers.js";
import { LogErrorStack } from "../../../utils/log.js";

/**
 * Calculates the appropriate battle timeout based on battle type and NPC characteristics
 */
const calculateBattleTimeout = (battleType: BattleType, target?: ExtUserModel | NPCUser | RooftopNPCUser): number => {
    switch (battleType) {
        case "pve":
        case "pve-explore": {
            const npcTarget = target as NPCUser;
            const isBoss = npcTarget?.boss || false;
            return Date.now() + (isBoss ? gameConfig.BOSS_BATTLE_TIMEOUT_MS : gameConfig.NORMAL_NPC_BATTLE_TIMEOUT_MS);
        }
        case "pvp":
        case "pve-rooftop":
        default: {
            return Date.now() + gameConfig.BATTLE_TIMEOUT_MS;
        }
    }
};

/**
 * Logs the appropriate action based on battle type
 */
const logBattleAction = (
    battleType: BattleType,
    userId: number,
    battleId: string,
    target: ExtUserModel | NPCUser | RooftopNPCUser
) => {
    if (battleType === "pvp") {
        logAction({
            action: "BATTLE_INITIATED",
            userId,
            info: {
                battleId,
                battleType,
                targetId: target.id,
                targetName: "username" in target ? target.username : target.name,
            },
        });
    } else {
        // NPC battles (pve, pve-explore, pve-rooftop)
        const npcTarget = target as NPCUser | RooftopNPCUser;
        logAction({
            action: "BATTLE_STARTED_NPC",
            userId,
            info: {
                npcId: npcTarget.id,
                npcName: npcTarget.name,
                npcLevel: npcTarget.level,
                npcHealth: npcTarget.currentHealth || npcTarget.health,
                isBoss: "boss" in npcTarget ? npcTarget.boss : false,
            },
        });
    }
};

export const initiateBattle = async (
    currentUser: ExtUserModel,
    target: ExtUserModel | NPCUser | RooftopNPCUser,
    battleType: BattleType,
    validUntil?: number
) => {
    // Validate battle conditions before proceeding
    const validationError = await BattleHelpers.GetBeginBattleError(currentUser, target, battleType);
    if (validationError) {
        return { error: validationError };
    }

    const currentUserId = currentUser.id.toString();
    const targetId = battleType === "pvp" ? target.id.toString() : `npc_${target.id}`;

    const battleId = generateBattleId(currentUserId, targetId, battleType);
    const updateValues: Prisma.userUpdateInput = {};

    // Update action points based on battle type
    if (battleType === "pvp") {
        updateValues.actionPoints = {
            decrement: gameConfig.PVP_BATTLE_AP_COST,
        };
    }

    if (battleType === "pve-rooftop") {
        updateValues.actionPoints = {
            decrement: gameConfig.ROOFTOP_BATTLE_AP_COST,
        };
    }

    await UserService.updateUser(currentUser.id, updateValues);

    // Calculate battle timeout if not provided
    const battleValidUntil = validUntil || calculateBattleTimeout(battleType, target);
    const battleState = await createBattleState(currentUser, target, battleId, battleValidUntil, battleType);

    if (!battleState) {
        LogErrorStack({
            error: new Error("Battle state not found"),
        });
        return { error: "Battle state not found" };
    }

    // Sanitize battle state for frontend
    const sanitizedBattleState = sanitizeBattleStateForFrontend(battleState);

    // Log the appropriate action
    logBattleAction(battleType, currentUser.id, battleState.id, target);

    return {
        data: {
            battleId,
            attackerId: currentUserId,
            defenderId: targetId,
            state: sanitizedBattleState,
        },
    };
};
