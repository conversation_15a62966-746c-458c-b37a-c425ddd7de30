import { promises as fs } from "node:fs";
import path from "node:path";
import { fileURLToPath } from "node:url";
import { getSchedulerConfig } from "./config.js";
import createQueueAndWorker from "./createQueueAndWorker.js";
import processDailyMissions from "./tasks/missions/processDailyMissions.js";
import checkDonationGoal from "./tasks/shrine/checkDonationGoal.js";
// import { CronParser } from "cron-parser";
import { logger } from "../../utils/log.js";
import { Queue, QueueEvents } from "bullmq";

// ESM replacement for __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// app.get("/queue", async function (req: Request, res: Response) {
//     res.header("Content-Type", "text/html");
//     return res.sendFile("queue.html", { root: "public" });
// });

// app.get("/queueData", async function (req: Request, res: Response) {
//     const status = await getQueuesStatus();
//     return res.status(200).json(status);
// });

// Store queues globally so we can access them for status
const queues = new Map<string, Queue>();
const queueEvents = new Map<string, QueueEvents>();

// Add event listeners to track job completion for each queue
function setupJobCompletionTracking(queue: Queue): void {
    const queueEvent = new QueueEvents(queue.name, { connection: queue.opts.connection });

    queueEvent.on("completed", ({ jobId }) => {
        logger.debug(`[SCHEDULER] Job ${jobId} completed successfully`);
    });

    queueEvent.on("failed", ({ jobId, failedReason }) => {
        logger.error(`[SCHEDULER] Job ${jobId} failed:` + failedReason);
    });

    queueEvents.set(queue.name, queueEvent);
}

async function runStartupTasks(): Promise<void> {
    await checkDonationGoal();
    await processDailyMissions();
}

export async function initializeScheduler(): Promise<void> {
    // Run startup tasks
    await runStartupTasks();

    const tasksDir = path.join(__dirname, "tasks");
    const directories = await fs.readdir(tasksDir);

    for (const dir of directories) {
        if (dir === "__tests__") continue;
        const indexPath = path.join(tasksDir, dir, "index.js");
        try {
            const tasks = await import(indexPath);

            // Get current config dynamically
            const config = getSchedulerConfig();

            // Check if dir is a valid key in config.tasks
            if (!(dir in config.tasks)) {
                logger.warn(`[SCHEDULER] No configuration found for task directory: ${dir}`);
                continue;
            }

            const taskSchedules = config.tasks[dir as keyof typeof config.tasks];

            const queue = await createQueueAndWorker(dir, tasks, taskSchedules);
            setupJobCompletionTracking(queue);
            queues.set(dir, queue);
        } catch (error) {
            logger.error(`[SCHEDULER] Error initializing queue ${dir}: ` + error);
        }
    }
    logger.debug("[SCHEDULER] All queues initialized");
}

/**
 * Gracefully closes all scheduler queues and their queue event listeners.
 * This should be called during application shutdown.
 */
export async function closeScheduler(): Promise<void> {
    logger.debug("[SCHEDULER] Closing all queues and queue events");

    // Close all queue events first
    const queueEventPromises = [];
    for (const [name, queueEvent] of queueEvents.entries()) {
        queueEventPromises.push(
            queueEvent
                .close()
                .then(() => logger.debug(`[SCHEDULER] Closed queue event for ${name}`))
                .catch((error) => logger.error(`[SCHEDULER] Error closing queue event for ${name}:`, error))
        );
    }

    // Wait for all queue events to close
    await Promise.allSettled(queueEventPromises);
    queueEvents.clear();

    // Close all queues
    const queuePromises = [];
    for (const [name, queue] of queues.entries()) {
        queuePromises.push(
            queue
                .close()
                .then(() => logger.debug(`[SCHEDULER] Closed queue ${name}`))
                .catch((error) => logger.error(`[SCHEDULER] Error closing queue ${name}:`, error))
        );
    }

    // Wait for all queues to close
    await Promise.allSettled(queuePromises);
    queues.clear();

    logger.debug("[SCHEDULER] All scheduler resources closed successfully");
}

// interface QueueJobStatus {
//     name: string;
//     nextRun: string;
//     status: "ACTIVE" | "FAILED";
//     pattern: string;
//     error?: string;
// }

// interface CompletedJob {
//     queueName: string;
//     name: string;
//     finishedAt: number;
//     status: "COMPLETED" | "FAILED";
//     duration: number;
//     error?: string;
// }

// interface UpcomingJob {
//     queueName: string;
//     name: string;
//     scheduledFor: number;
//     data: unknown;
// }

// type QueueStatus = Record<string, QueueJobStatus[]>;

// interface SchedulerStatus {
//     queues: QueueStatus;
//     completedJobs: CompletedJob[];
//     upcomingJobs: UpcomingJob[];
// }

// export async function getRecentCompletedJobs() {
//     const allCompletedJobs = [];

//     for (const [queueName, queue] of queues.entries()) {
//         try {
//             // Get both completed and failed jobs from the last 24 hours
//             const completedJobs = await queue.getJobs(["completed", "failed"], 0, 100);

//             for (const job of completedJobs) {
//                 // Only include jobs that have actually finished
//                 if (job.finishedOn) {
//                     allCompletedJobs.push({
//                         queueName,
//                         name: job.name,
//                         finishedAt: job.finishedOn,
//                         status: job.failedReason ? "FAILED" : "COMPLETED",
//                         duration: job.finishedOn - job.processedOn,
//                         error: job.failedReason,
//                     });
//                 }
//             }
//         } catch (error) {
//             logger.error(`[SCHEDULER] Error getting completed jobs for queue ${queueName}:` + error);
//         }
//     }

//     // Sort by finished time descending and take last 5
//     return allCompletedJobs.sort((a, b) => b.finishedAt - a.finishedAt).slice(0, 5);
// }

// export async function getUpcomingJobs() {
//     const allUpcomingJobs = [];

//     for (const [queueName, queue] of queues.entries()) {
//         try {
//             const waitingJobs = await queue.getJobs(["waiting"], 0, 10);

//             for (const job of waitingJobs) {
//                 allUpcomingJobs.push({
//                     queueName,
//                     name: job.name,
//                     scheduledFor: job.timestamp,
//                     data: job.data,
//                 });
//             }
//         } catch (error) {
//             logger.error(`[SCHEDULER] Error getting upcoming jobs for queue ${queueName}:` + error);
//         }
//     }

//     // Sort by scheduled time ascending
//     return allUpcomingJobs.sort((a, b) => a.scheduledFor - b.scheduledFor);
// }

// export async function getQueuesStatus(): Promise<SchedulerStatus> {
//     return null;
// const status: QueueStatus = {};
// const [completedJobs, upcomingJobs] = await Promise.all([getRecentCompletedJobs(), getUpcomingJobs()]);

// for (const [queueName, queue] of queues.entries()) {
//     const jobs = await queue.getRepeatableJobs();
//     const failedJobs = (await queue.getJobs(["failed"])) || [];
//     const failedJobNames = new Set(failedJobs.map((job: Job) => job.name));

//     status[queueName] = jobs.map((job) => {
//         // Parse the next run time
//         let nextRun: Date;
//         try {
//             nextRun = getNow(job.next);
//             // If next is invalid, calculate from the cron pattern or every value
//             if (Number.isNaN(nextRun.getTime())) {
//                 if (job.cron) {
//                     const interval = CronParser.parseExpression(job.cron);
//                     nextRun = interval.next().toDate();
//                 } else if (job.every) {
//                     nextRun = new Date(Date.now() + job.every);
//                 }
//             }
//         } catch (error) {
//             nextRun = new Date(); // Fallback to current time if parsing fails
//             logger.error(`[SCHEDULER] Error parsing next run time for job ${job.name}:`, error);
//         }

//         return {
//             name: job.name,
//             nextRun: nextRun.toISOString(),
//             status: failedJobNames.has(job.name) ? "FAILED" : "ACTIVE",
//             pattern: job.cron || `Every ${job.every}ms`,
//             error: failedJobs.find((fj: Job) => fj.name === job.name)?.failedReason,
//         };
//     });
// }

// return {
//     queues: status,
//     completedJobs,
//     upcomingJobs,
// };
// }

export async function resetAllQueues(): Promise<void> {
    try {
        // Clean up existing queues
        for (const [queueName, queue] of queues.entries()) {
            // Remove all jobs
            await queue.obliterate({ force: true });

            // Re-initialize the queue with current configuration
            const tasks = await import(path.join(__dirname, "tasks", queueName, "index.js"));

            // Get current config dynamically
            const config = getSchedulerConfig();

            // Check if queueName is a valid key in config.tasks
            if (!(queueName in config.tasks)) {
                logger.warn(`[SCHEDULER] No configuration found for queue: ${queueName}`);
                continue;
            }

            const taskSchedules = config.tasks[queueName as keyof typeof config.tasks];

            // Create new queue and worker
            const newQueue = await createQueueAndWorker(queueName, tasks, taskSchedules);
            queues.set(queueName, newQueue);
        }

        logger.debug("[SCHEDULER] All queues have been reset successfully");
    } catch (error) {
        logger.error("[SCHEDULER] Error resetting queues: " + error);
        throw error;
    }
}
