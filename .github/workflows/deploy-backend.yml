name: Deploy Backend

on:
  push:
    branches: [main, test]
    paths:
      - 'chikara-backend/**'
      - 'package.json'
      - 'turbo.json'

jobs:
  deploy:
    name: Deploy to Coolify
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/test'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Send webhook to Coolify
        env:
          COOLIFY_WEBHOOK_URL: ${{ secrets.COOLIFY_WEBHOOK_URL }}
        run: |
          if [ -z "$COOLIFY_WEBHOOK_URL" ]; then
            echo "❌ COOLIFY_WEBHOOK_URL secret not set"
            exit 1
          fi
          
          echo "🚀 Sending deployment webhook to Coolify..."
          
          response=$(curl -s -w "%{http_code}" -X POST "$COOLIFY_WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d '{
              "ref": "'${{ github.ref }}'",
              "repository": "'${{ github.repository }}'",
              "commit": "'${{ github.sha }}'",
              "branch": "'${{ github.ref_name }}'",
              "pusher": "'${{ github.actor }}'",
              "message": "'${{ github.event.head_commit.message }}'"
            }')
          
          http_code="${response: -3}"
          response_body="${response%???}"
          
          if [ "$http_code" -ge 200 ] && [ "$http_code" -lt 300 ]; then
            echo "✅ Deployment webhook sent successfully (HTTP $http_code)"
            echo "Response: $response_body"
          else
            echo "❌ Deployment webhook failed (HTTP $http_code)"
            echo "Response: $response_body"
            exit 1
          fi

      - name: Wait for deployment
        run: |
          echo "⏳ Waiting for deployment to complete..."
          echo "Check your Coolify dashboard for deployment status"