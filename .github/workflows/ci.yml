name: CI

on:
  push:
    branches: [main, test, develop]
  pull_request:
    branches: [main, test]

env:
  BUN_VERSION: 1.2.19

jobs:
  changes:
    name: Detect changes
    runs-on: ubuntu-latest
    outputs:
      frontend: ${{ steps.changes.outputs.frontend }}
      backend: ${{ steps.changes.outputs.backend }}
      admin: ${{ steps.changes.outputs.admin }}
      landing: ${{ steps.changes.outputs.landing }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for changes
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            frontend:
              - 'chikara-frontend/**'
              - 'package.json'
              - 'turbo.json'
            backend:
              - 'chikara-backend/**'
              - 'package.json'
              - 'turbo.json'
            admin:
              - 'admin-panel/**'
              - 'package.json'
              - 'turbo.json'
            landing:
              - 'chikara-landing/**'
              - 'package.json'
              - 'turbo.json'

  setup:
    name: Setup dependencies
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.bun/install/cache
            node_modules
            chikara-frontend/node_modules
            chikara-backend/node_modules
            admin-panel/node_modules
            chikara-landing/node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb', '**/package.json') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: bun install --frozen-lockfile

  lint-and-type-check:
    name: Lint and Type Check
    runs-on: ubuntu-latest
    needs: [setup, changes]
    if: needs.changes.outputs.frontend == 'true' || needs.changes.outputs.backend == 'true' || needs.changes.outputs.admin == 'true' || needs.changes.outputs.landing == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.bun/install/cache
            node_modules
            chikara-frontend/node_modules
            chikara-backend/node_modules
            admin-panel/node_modules
            chikara-landing/node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb', '**/package.json') }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Lint
        run: bun run lint

      - name: Type check
        run: bun run type-check

  test:
    name: Test
    runs-on: ubuntu-latest
    needs: [setup, changes]
    if: needs.changes.outputs.frontend == 'true' || needs.changes.outputs.backend == 'true' || needs.changes.outputs.admin == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.bun/install/cache
            node_modules
            chikara-frontend/node_modules
            chikara-backend/node_modules
            admin-panel/node_modules
            chikara-landing/node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb', '**/package.json') }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run tests
        run: bun run test

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [setup, changes, lint-and-type-check]
    if: needs.changes.outputs.frontend == 'true' || needs.changes.outputs.backend == 'true' || needs.changes.outputs.admin == 'true' || needs.changes.outputs.landing == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: ${{ env.BUN_VERSION }}

      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: |
            ~/.bun/install/cache
            node_modules
            chikara-frontend/node_modules
            chikara-backend/node_modules
            admin-panel/node_modules
            chikara-landing/node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb', '**/package.json') }}

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build
        run: bun run build